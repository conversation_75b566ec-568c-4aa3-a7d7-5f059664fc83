"use client"
import React, { useState, useMemo } from "react";
import CreateTrackSheet from "./createTrackSheet";
import { TrackSheetContext } from "./TrackSheetContext";
import Sidebar from "@/components/sidebar/Sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import BreadCrumbs from "@/app/_component/BreadCrumbs";
import TrackSheetTabs from "./TrackSheetTabs";
import ImportModal from "./ImportModal";
import ClientSelectPage from "./ClientSelectPage";
import SearchSelect from "@/app/_component/SearchSelect";

const ManageWorkSheet = ({
  permissions,
  client,
  associate,
  userData,
  actions,
  carrierDataUpdate,
  clientDataUpdate,
  carrier
}: any) => {
  const [filterdata, setFilterData] = useState([]);
  const [deleteData, setDeletedData] = useState(false);
  const [activeView, setActiveView] = useState("view");
  const [customFieldsReloadTrigger, setCustomFieldsReloadTrigger] = useState(0);
  const [isImportModalOpen, setImportModalOpen] = useState(false);
  const [selectedAssociateId, setSelectedAssociateId] = useState("");
  const [selectedClientId, setSelectedClientId] = useState("");

  const associateOptions = associate?.map((a: any) => ({
    value: a.id?.toString(),
    label: a.name,
    name: a.name,
  })) || [];

  const clientOptions = useMemo(() => {
    if (!selectedAssociateId) {
      return client?.map((c: any) => ({
        value: c.id?.toString(),
        label: c.client_name,
        name: c.client_name,
      })) || [];
    }
    const filteredClients =
      client?.filter(
        (c: any) => c.associateId?.toString() === selectedAssociateId
      ) || [];
    return filteredClients.map((c: any) => ({
      value: c.id?.toString(),
      label: c.client_name,
      name: c.client_name,
    }));
  }, [client, selectedAssociateId]);

  const contextValue = useMemo(() => ({
    filterdata,
    setFilterData,
    deleteData,
    setDeletedData,
    setCustomFieldsReloadTrigger,
    customFieldsReloadTrigger,
  }), [filterdata, setFilterData, deleteData, setDeletedData, setCustomFieldsReloadTrigger, customFieldsReloadTrigger]);

  return (
    <TrackSheetContext.Provider value={contextValue}>
      <SidebarProvider>
        <div className="flex w-full min-h-screen">
          <Sidebar permissions={permissions} profile={userData} />
          <main className="flex-1 w-full pl-3 overflow-auto">
            <div className="mb-2">
              <BreadCrumbs
                breadcrumblist={[
                  { link: "/user/trackSheets", name: "TrackSheet" },
                ]}
              />
            </div>
            <TrackSheetTabs
              activeView={activeView}
              setActiveView={setActiveView}
              isImportModalOpen={isImportModalOpen}
              setImportModalOpen={setImportModalOpen}
              selectedAssociateId={selectedAssociateId}
              setSelectedAssociateId={setSelectedAssociateId}
              selectedClientId={selectedClientId}
              setSelectedClientId={setSelectedClientId}
              associateOptions={associateOptions}
              clientOptions={clientOptions}
            />
            <ImportModal
              isOpen={isImportModalOpen}
              onClose={() => setImportModalOpen(false)}
              userData={userData}
            />
            {activeView === "create" ? (
              <CreateTrackSheet
                client={client}
                associate={associate}
                userData={userData}
                activeView={activeView}
                setActiveView={setActiveView}
                permissions={actions}
                carrierDataUpdate={carrierDataUpdate}
                clientDataUpdate={clientDataUpdate}
                carrier={carrier}
                associateId={selectedAssociateId}
                clientId={selectedClientId}
              />
            ) : (
              <div className="w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1">
                <ClientSelectPage
                  permissions={actions}
                  client={client}
                  clientDataUpdate={clientDataUpdate}
                  carrierDataUpdate={carrierDataUpdate}
                  userData={userData}
                />
              </div>
            )}
          </main>
        </div>
      </SidebarProvider>
    </TrackSheetContext.Provider>
  );
};

export default ManageWorkSheet;