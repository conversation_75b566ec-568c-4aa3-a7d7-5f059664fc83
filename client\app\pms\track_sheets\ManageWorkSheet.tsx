"use client";
import React, { useState } from "react";
import { TrackSheetContext } from "./TrackSheetContext";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import ClientSelectPage from "./ClientSelectPage";
import { PermissionWrapper } from "@/lib/permissionWrapper";

const ManageWorkSheet = ({
  permissions,
  client,
  carrierDataUpdate,
  clientDataUpdate,
}: any) => {
  const [filterdata, setFilterData] = useState([]);
  const [deleteData, setDeletedData] = useState(false);
  const [customFieldsReloadTrigger, setCustomFieldsReloadTrigger] = useState(0);

  return (
    <TrackSheetContext.Provider
      value={{
        filterdata,
        setFilterData,
        deleteData,
        setDeletedData,
        setCustomFieldsReloadTrigger,
        customFieldsReloadTrigger,
      }}
    >
      <div className="ml-3">
        <div className="h-9 flex items-center">
          <AdminNavBar link={"/pms/track_sheets"} name={"TrackSheet"} />
        </div>
        <div className="space-y-2 mt-2 ">
          <h2 className="text-2xl">TrackSheet</h2>
        </div>
        <PermissionWrapper
          permissions={permissions}
          requiredPermissions={["View-TrackSheet"]}
        >
          <div className="w-full  animate-in fade-in duration-1000 mt-2">
            <ClientSelectPage
              permissions={permissions}
              client={client}
              carrierDataUpdate={carrierDataUpdate}
              clientDataUpdate={clientDataUpdate}
            />
          </div>
        </PermissionWrapper>
      </div>
    </TrackSheetContext.Provider>
  );
};

export default ManageWorkSheet;
