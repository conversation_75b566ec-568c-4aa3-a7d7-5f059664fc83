"use client";
import React, {
  useState,
  useCallback,
  useRef,
  useEffect,
  useMemo,
} from "react";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import FormInput from "@/app/_component/FormInput";
import FormDatePicker from "@/app/_component/FormDatePicker";
import FormCheckboxGroup from "@/app/_component/FormCheckboxGroup";
import { Button } from "@/components/ui/button";
import {
  // MinusCircle,
  // PlusCircle,
  Info,
  DollarSign,
  FileText,
  Building2,
  Hash,
} from "lucide-react";
import { formSubmit, getAllData } from "@/lib/helpers";
import {
  clientCustomFields_routes,
  trackSheets_routes,
  legrandMapping_routes,
  manualMatchingMapping_routes,
  customFilepath_routes,
  carrier_routes,
} from "@/lib/routePath";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { z } from "zod";
import SearchSelect from "@/app/_component/SearchSelect";
import PageInput from "@/app/_component/PageInput";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import LegrandDetailsComponent from "./LegrandDetailsComponent";
import ClientSelectPage from "./ClientSelectPage";
import ImportModal from "./ImportModal";
import { Select } from "@/components/ui/select";

const FIELD_OPTIONS = [
  "ASSOCIATE",
  "CLIENT",
  "ADDITIONALFOLDERNAME",
  "CARRIER",
  "YEAR",
  "MONTH",
  "RECEIVE DATE",
  "FTP FILE NAME",
];

const isField = (value: string) => FIELD_OPTIONS.includes(value);

const validateFtpPageFormat = (value: string): boolean => {
  if (!value || value.trim() === "") return false;

  const ftpPageRegex = /^(\d+)\s+of\s+(\d+)$/i;
  const match = value.match(ftpPageRegex);

  if (!match) return false;

  const currentPage = parseInt(match[1], 10);
  const totalPages = parseInt(match[2], 10);

  return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;
};

const validateDateFormat = (value: string): boolean => {
  if (!value || value.trim() === "") return true; // Empty is valid for optional fields

  const dateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
  const match = value.match(dateRegex);

  if (!match) return false;

  const day = parseInt(match[1], 10);
  const month = parseInt(match[2], 10);
  const year = parseInt(match[3], 10);

  // Basic range checks
  if (month < 1 || month > 12) return false;
  if (day < 1 || day > 31) return false;
  if (year < 1900 || year > 2100) return false;

  // Create date and check if it's valid
  const date = new Date(year, month - 1, day);
  return (
    date.getFullYear() === year &&
    date.getMonth() === month - 1 &&
    date.getDate() === day
  );
};

const trackSheetSchema = z
  .object({
    clientId: z.string().min(1, "Client is required"),
    entries: z.array(
      z.object({
        company: z.string().min(1, "Company is required"),
        division: z.string().optional(),
        invoice: z.string().min(1, "Invoice is required"),
        masterInvoice: z.string().optional(),
        bol: z.string().optional(),
        invoiceDate: z
          .string()
          .min(1, "Invoice date is required")
          .refine(
            validateDateFormat,
            "Please enter a valid date in DD/MM/YYYY format"
          ),
        receivedDate: z
          .string()
          .min(1, "Received date is required")
          .refine(
            validateDateFormat,
            "Please enter a valid date in DD/MM/YYYY format"
          ),
        shipmentDate: z
          .string()
          .optional()
          .refine(
            (value) => !value || validateDateFormat(value),
            "Please enter a valid date in DD/MM/YYYY format"
          ),
        carrierName: z.string().min(1, "Carrier name is required"),
        invoiceStatus: z.string().min(1, "Invoice status is required"),
        manualMatching: z.string().min(1, "Manual matching is required"),
        invoiceType: z.string().min(1, "Invoice type is required"),
        billToClient: z.string().optional(),
        currency: z.string().min(1, "Currency is required"),
        qtyShipped: z.string().optional(),
        weightUnitName: z.string().optional(),
        quantityBilledText: z.string().optional(),
        freightClass: z.string().optional(),
        invoiceTotal: z.string().min(1, "Invoice total is required"),
        savings: z.string().optional(),
        financialNotes: z.string().optional(),
        ftpFileName: z.string().min(1, "FTP File Name is required"),
        ftpPage: z
          .string()
          .min(1, "FTP Page is required")
          .refine(
            (value) => validateFtpPageFormat(value),
            (value) => {
              if (!value || value.trim() === "") {
                return { message: "FTP Page is required" };
              }

              const ftpPageRegex = /^(\d+)\s+of\s+(\d+)$/i;
              const match = value.match(ftpPageRegex);

              if (!match) {
                return { message: "" };
              }

              const currentPage = parseInt(match[1], 10);
              const totalPages = parseInt(match[2], 10);

              if (currentPage <= 0 || totalPages <= 0) {
                return {
                  message: "Page numbers must be positive (greater than 0)",
                };
              }

              if (currentPage > totalPages) {
                return {
                  message: `Please enter a page number between ${totalPages} and ${currentPage} `,
                };
              }

              return { message: "Invalid page format" };
            }
          ),
        docAvailable: z.array(z.string()).optional().default([]),
        otherDocuments: z.string().optional(),
        notes: z.string().optional(),
        //mistake: z.string().optional(),
        legrandAlias: z.string().optional(),
        legrandCompanyName: z.string().optional(),
        legrandAddress: z.string().optional(),
        legrandZipcode: z.string().optional(),
        shipperAlias: z.string().optional(),
        shipperAddress: z.string().optional(),
        shipperZipcode: z.string().optional(),
        consigneeAlias: z.string().optional(),
        consigneeAddress: z.string().optional(),
        consigneeZipcode: z.string().optional(),
        billtoAlias: z.string().optional(),
        billtoAddress: z.string().optional(),
        billtoZipcode: z.string().optional(),
        customFields: z
          .array(
            z.object({
              id: z.string(),
              name: z.string(),
              type: z.string().optional(),
              value: z.string().optional(),
            })
          )
          .default([]),
        enteredBy: z.string().optional(),
      }).refine(
        (entry) => {
          // Only validate if both dates are present and valid
          if (validateDateFormat(entry.invoiceDate) && validateDateFormat(entry.receivedDate)) {
            const [invDay, invMonth, invYear] = entry.invoiceDate.split("/").map(Number);
            const [recDay, recMonth, recYear] = entry.receivedDate.split("/").map(Number);
            const invoiceDateObj = new Date(invYear, invMonth - 1, invDay);
            const receivedDateObj = new Date(recYear, recMonth - 1, recDay);
            return invoiceDateObj <= receivedDateObj;
          }
          return true; // If not both valid, let other validators handle
        },
        {
          message: "The invoice date should be older than or the same as the received date.",
          path: ["invoiceDate"],
        }
      )
    ),
  })
  .refine(
    (data) => {
      // Custom validation for LEGRAND clients - freightClass is required
      for (let i = 0; i < data.entries.length; i++) {
        const entry = data.entries[i];
        if (entry.company === "LEGRAND" || entry.company?.includes("LEGRAND")) {
          if (!entry.freightClass || entry.freightClass.trim() === "") {
            return false;
          }
        }
      }
      return true;
    },
    {
      message: "Freight Class is required for LEGRAND clients",
      path: ["entries"],
    }
  );

type CustomField = {
  id: string;
  name: string;
  type?: string;
  value?: string;
};

type FormValues = {
  associateId: string;
  clientId: string;
  entries: Array<{
    company: string;
    division: string;
    invoice: string;
    masterInvoice: string;
    bol: string;
    invoiceDate: string;
    receivedDate: string;
    shipmentDate: string;
    carrierName: string;
    invoiceStatus: string;
    manualMatching: string;
    invoiceType: string;
    billToClient: string;
    currency: string;
    qtyShipped: string;
    weightUnitName: string;
    quantityBilledText: string;
    freightClass: string;
    invoiceTotal: string;
    savings: string;
    financialNotes: string;
    ftpFileName: string;
    ftpPage: string;
    docAvailable: string[];
    otherDocuments: string;
    notes: string;
    //mistake: string;
    legrandAlias?: string;
    legrandCompanyName?: string;
    legrandAddress?: string;
    legrandZipcode?: string;
    shipperAlias?: string;
    shipperAddress?: string;
    shipperZipcode?: string;
    consigneeAlias?: string;
    consigneeAddress?: string;
    consigneeZipcode?: string;
    billtoAlias?: string;
    billtoAddress?: string;
    billtoZipcode?: string;
    customFields?: CustomField[];
    enteredBy?: string;
  }>;
};

const CreateTrackSheet = ({
  client,
  associate,
  userData,
  activeView,
  setActiveView,
  permissions,
  carrierDataUpdate,
  clientDataUpdate,
  carrier,
  associateId,
  clientId,
}: any) => {
  const userName = userData?.username;
  const companyFieldRefs = useRef<(HTMLElement | null)[]>([]);
  const [isImportModalOpen, setImportModalOpen] = useState(false);
  const [clientFilePathFormat, setClientFilePathFormat] = useState<
    string | null
  >(null);
  const [existingEntries, setExistingEntries] = useState<{
    [key: string]: boolean;
  }>({});

  const [generatedFilenames, setGeneratedFilenames] = useState<string[]>([]);
  const [filenameValidation, setFilenameValidation] = useState<boolean[]>([]);
  const [missingFields, setMissingFields] = useState<string[][]>([]);
  const [legrandData, setLegrandData] = useState<any[]>([]);
  const [manualMatchingData, setManualMatchingData] = useState<any[]>([]);
  const [customFieldsRefresh, setCustomFieldsRefresh] = useState<number>(0);
  const [showFullForm, setShowFullForm] = useState(false);

  // Mapping placeholders to form fields
  const placeholderMap = {
    FTPFILENAME: "ftpFileName",
    FTPPAGE: "ftpPage",
    ENTEREDBY: "enteredBy",
  };

  const selectionForm = useForm({
    defaultValues: {
      associateId: "",
      clientId: "",
    },
  });

  const associateOptions = associate?.map((a: any) => ({
    value: a.id?.toString(),
    label: a.name,
    name: a.name,
  }));

  const [carrierByClient, setCarrierByClient] = useState([]);

  const handleChange = async (id: string) => {
    try {
      const carrierByClientData = await getAllData(
        `${carrier_routes.GET_CARRIER_BY_CLIENT}/${id}`
      );
      if (carrierByClientData && Array.isArray(carrierByClientData)) {
        // Transform the data to match the expected format
        const formattedCarriers = carrierByClientData.map((item: any) => ({
          value: item.carrier?.id?.toString(),
          label: item.carrier?.name,
        })).filter((carrier: any) => carrier.value && carrier.label);

        // Sort carriers by name
        formattedCarriers.sort((a: any, b: any) => 
          a.label.localeCompare(b.label)
        );

        setCarrierByClient(formattedCarriers);
      } else {
        setCarrierByClient([]);
      }
    } catch (error) {
      console.error("Error fetching carrier data:", error);
      setCarrierByClient([]);
    }
  };

  // const carrierOptions = carrier?.map((c: any) => ({
  //   value: c.id?.toString(),
  //   label: c.name,
  // }));

  const router = useRouter();

  const form = useForm({
    resolver: zodResolver(trackSheetSchema),
    defaultValues: {
      associateId: associateId || "",
      clientId: clientId || "",
      entries: [
        {
          company: "",
          division: "",
          invoice: "",
          masterInvoice: "",
          bol: "",
          invoiceDate: "",
          receivedDate: "",
          shipmentDate: "",
          carrierName: "",
          invoiceStatus: "ENTRY",
          manualMatching: "",
          invoiceType: "",
          billToClient: "yes",
          currency: "",
          qtyShipped: "",
          weightUnitName: "",
          quantityBilledText: "",
          freightClass: "",
          invoiceTotal: "",
          savings: "",
          financialNotes: "",
          ftpFileName: "",
          ftpPage: "",
          docAvailable: [],
          otherDocuments: "",
          notes: "",
          legrandAlias: "",
          legrandCompanyName: "",
          legrandAddress: "",
          legrandZipcode: "",
          shipperAlias: "",
          shipperAddress: "",
          shipperZipcode: "",
          consigneeAlias: "",
          consigneeAddress: "",
          consigneeZipcode: "",
          billtoAlias: "",
          billtoAddress: "",
          billtoZipcode: "",
          customFields: [],
          enteredBy: userData?.username || "",
        },
      ],
    },
  });

  // Add form value tracking
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (name?.includes("receivedDate") || name?.includes("ftpFileName")) {
        // Form value tracking without console logs
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Update the form fields to handle changes
  const handleDateChange = useCallback(
    (index: number, value: string) => {
      form.setValue(`entries.${index}.receivedDate`, value, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    },
    [form]
  );

  const handleFtpFileNameChange = useCallback(
    (index: number, value: string) => {
      form.setValue(`entries.${index}.ftpFileName`, value, {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    },
    [form]
  );

  const clientOptions = useMemo(() => {
    if (!associateId) {
      return (
        client?.map((c: any) => ({
          value: c.id?.toString(),
          label: c.client_name,
          name: c.client_name,
        })) || []
      );
    }
    const filteredClients =
      client?.filter(
        (c: any) => c.associateId?.toString() === associateId
      ) || [];

    return filteredClients.map((c: any) => ({
      value: c.id?.toString(),
      label: c.client_name,
      name: c.client_name,
    }));
  }, [client, associateId]);

  const entries = useWatch({ control: form.control, name: "entries" });
  const watchedClientId = useWatch({ control: form.control, name: "clientId" });

  const validateClientForAssociate = useCallback(
    (associateId: string, currentClientId: string) => {
      if (associateId && currentClientId) {
        const currentClient = client?.find(
          (c: any) => c.id?.toString() === currentClientId
        );
        if (
          currentClient &&
          currentClient.associateId?.toString() !== associateId
        ) {
          form.setValue("clientId", "");
          return false;
        }
      }
      return true;
    },
    [client, form]
  );

  const clearEntrySpecificClients = useCallback(() => {
    const currentEntries = form.getValues("entries") || [];
    if (currentEntries.length > 0) {
      const hasEntrySpecificClients = currentEntries.some(
        (entry: any) => entry.clientId
      );
      if (hasEntrySpecificClients) {
        const updatedEntries = currentEntries.map((entry: any) => ({
          ...entry,
          clientId: "",
        }));
        form.setValue("entries", updatedEntries);
      }
    }
  }, [form]);

  const fetchClientFilePathFormat = async (clientId: string) => {
    try {
      const response = await fetch(
        `${customFilepath_routes.GET_CLIENT_CUSTOM_FILEPATH}?clientId=${clientId}`
      );
      if (response.ok) {
        const result = await response.json();
        if (
          result.success &&
          result.data &&
          Array.isArray(result.data) &&
          result.data.length > 0
        ) {
          const filepathData = result.data[0];
          if (filepathData && filepathData.filePath) {
            setClientFilePathFormat(filepathData.filePath);
          } else {
            setClientFilePathFormat(null);
          }
        } else {
          setClientFilePathFormat(null);
        }
      } else {
        setClientFilePathFormat(null);
      }
    } catch (error) {
      console.error("Error fetching filePath for client:", error);
      setClientFilePathFormat(null);
    }
  };

  const fetchLegrandData = useCallback(async () => {
    try {
      const response = await getAllData(
        legrandMapping_routes.GET_LEGRAND_MAPPINGS
      );
      if (response && Array.isArray(response)) {
        setLegrandData(response);
      }
    } catch (error) {
      toast.error("Error fetching LEGRAND mapping data:", error);
    }
  }, []);

  const fetchManualMatchingData = useCallback(async () => {
    try {
      const response = await getAllData(
        manualMatchingMapping_routes.GET_MANUAL_MATCHING_MAPPINGS
      );
      if (response && Array.isArray(response)) {
        setManualMatchingData(response);
      }
    } catch (error) {
      toast.error("Error fetching manual matching mapping data:", error);
    }
  }, []);

  useEffect(() => {
    if (!watchedClientId) {
      setLegrandData([]);
      setManualMatchingData([]);
      return;
    }
    const selectedClient = clientOptions.find((c: any) => c.value === watchedClientId);
    if (selectedClient && selectedClient.name === "LEGRAND") {
      fetchLegrandData();
      fetchManualMatchingData();
    } else {
      setLegrandData([]);
      setManualMatchingData([]);
    }
  }, [watchedClientId, clientOptions, fetchLegrandData, fetchManualMatchingData]);

  const handleLegrandDataChange = (
    entryIndex: number,
    businessUnit: string,
    divisionCode: string
  ) => {
    form.setValue(`entries.${entryIndex}.company`, businessUnit);

    if (divisionCode) {
      form.setValue(`entries.${entryIndex}.division`, divisionCode);
      handleManualMatchingAutoFill(entryIndex, divisionCode);
    } else {
      form.setValue(`entries.${entryIndex}.division`, "");
      form.setValue(`entries.${entryIndex}.manualMatching`, "");
    }
  };

  const handleManualMatchingAutoFill = useCallback(
    (entryIndex: number, division: string) => {
      if (!division || !manualMatchingData.length) {
        return;
      }

      const formValues = form.getValues();
      const entry = formValues.entries?.[entryIndex] as any;
      const entryClientId =
        entry?.clientId || (entryIndex === 0 ? formValues.clientId : "");
      const entryClientName =
        clientOptions?.find((c: any) => c.value === entryClientId)?.name || "";

      if (entryClientName !== "LEGRAND") {
        return;
      }

      const matchingEntry = manualMatchingData.find(
        (mapping: any) => mapping.division === division
      );

      if (matchingEntry && matchingEntry.ManualShipment) {
        form.setValue(
          `entries.${entryIndex}.manualMatching`,
          matchingEntry.ManualShipment
        );
      } else {
        form.setValue(`entries.${entryIndex}.manualMatching`, "");
      }
    },
    [form, manualMatchingData, clientOptions]
  );

  const fetchCustomFieldsForClient = useCallback(
    async (clientId: string) => {
      if (!clientId) return [];

      try {
        const allCustomFieldsResponse = await getAllData(
          `${clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS}/${clientId}`
        );

        let customFieldsData: any[] = [];
        if (
          allCustomFieldsResponse &&
          allCustomFieldsResponse.custom_fields &&
          allCustomFieldsResponse.custom_fields.length > 0
        ) {
          customFieldsData = allCustomFieldsResponse.custom_fields.map(
            (field: any) => {
              let autoFilledValue = "";

              if (field.type === "AUTO") {
                if (field.autoOption === "DATE") {
                  const today = new Date();
                  const day = today.getDate().toString().padStart(2, "0");
                  const month = (today.getMonth() + 1)
                    .toString()
                    .padStart(2, "0");
                  const year = today.getFullYear();
                  autoFilledValue = `${day}/${month}/${year}`;
                } else if (field.autoOption === "USERNAME") {
                  autoFilledValue = userData?.username || "";
                }
              }

              return {
                id: field.id,
                name: field.name,
                type: field.type,
                autoOption: field.autoOption,
                value: autoFilledValue,
              };
            }
          );
        }

        return customFieldsData;
      } catch (error) {
        return [];
      }
    },
    [userData]
  );

  // const { fields, append, remove } = useFieldArray({
  //   control: form.control,
  //   name: "entries",
  // });

  // For now, we'll work with a single entry
  const fields = [{ id: "single-entry" }];
  // const append = () => {};
  // const remove = () => {};

  useEffect(() => {
    if (watchedClientId) {
      fetchClientFilePathFormat(watchedClientId);
      handleChange(watchedClientId);
    } else {
      setClientFilePathFormat(null);
      setCarrierByClient([]);
    }
  }, [watchedClientId]);

  useEffect(() => {
    companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);
  }, [fields.length]);

  const generateFilename = useCallback(
    (entryIndex: number, formValues: any) => {
      try {
        const entry = formValues.entries[entryIndex];
        if (!entry)
          return { filename: "", isValid: false, missing: ["Entry data"] };

        // If no client is selected or no filepath format is available, return empty
        if (!clientFilePathFormat) {
          return {
            filename: "",
            isValid: false,
            missing: ["No client selected"],
          };
        }

        const missing = [];

        const pattern = clientFilePathFormat;

        const placeholderRegex = /{([^}]+)}|([A-Z]+(?:\s+[A-Z]+)*)(?=\/|\.|$)/g;
        const placeholders = Array.from(pattern.matchAll(placeholderRegex)).map(
          (match) => match[1] || match[2]
        );

        // Get current date information
        const now = new Date();
        const currentYear = now.getFullYear().toString();
        // Get month from received date if available, otherwise use current month
        let month = now
          .toLocaleString("default", { month: "long" })
          .toUpperCase();
        if (entry.receivedDate) {
          try {
            const [day, monthNum, year] = entry.receivedDate.split("/");
            if (day && monthNum && year) {
              const receivedDate = new Date(
                parseInt(year),
                parseInt(monthNum) - 1,
                parseInt(day)
              );
              month = receivedDate
                .toLocaleString("default", { month: "long" })
                .toUpperCase();
            }
          } catch (error) {
            console.error("Error formatting received date month:", error);
          }
        }

        // Get associate and client information
        const selectedAssociate = associate?.find(
          (a: any) => a.id?.toString() === formValues.associateId
        );
        const associateName = selectedAssociate?.name || "";

        const entryClientId = entry?.clientId || formValues.clientId;
        const selectedClient = client?.find(
          (c: any) => c.id?.toString() === entryClientId
        );
        const clientName = selectedClient?.client_name || "";

       // Get carrier information
       let carrierName = "";
       if (entry.carrierName) {
         const carrierOption = carrier?.find(
           (c: any) => c.id?.toString() === entry.carrierName
         );
         
         carrierName = carrierOption?.carrier_2nd_name || "";
       }

        // Format received date if it exists
        let formattedReceivedDate = "";
        if (entry.receivedDate) {
          try {
            // Convert DD/MM/YYYY to YYYY-MM-DD for file path
            const [day, month, year] = entry.receivedDate.split("/");
            if (day && month && year) {
              formattedReceivedDate = `${year}-${month.padStart(
                2,
                "0"
              )}-${day.padStart(2, "0")}`;
            }
          } catch (error) {
            console.error("Error formatting received date:", error);
          }
        }

        // Format FTP file name
        let formattedFtpFileName = entry.ftpFileName || "";
        if (formattedFtpFileName) {
          // Remove any existing .pdf extension since it's in the pattern
          formattedFtpFileName = formattedFtpFileName.replace(/\.pdf$/i, "");
        }

        // Create replacements object with all possible form fields
        const replacements: { [key: string]: string } = {
          // Match exact placeholders from pattern
          ASSOCIATE: associateName,
          CLIENT: clientName,
          CARRIER: carrierName,
          YEAR: currentYear,
          MONTH: month,
          "RECEIVE DATE": formattedReceivedDate,
          "FTP FILE NAME": formattedFtpFileName,
          FTPPAGE: entry.ftpPage || "",
          ENTEREDBY: entry.enteredBy || userData?.username || "",
          COMPANY: entry.company || "",
          DIVISION: entry.division || "",
          INVOICE: entry.invoice || "",
          MASTERINVOICE: entry.masterInvoice || "",
          BOL: entry.bol || "",
          INVOICEDATE: entry.invoiceDate || "",
          SHIPMENTDATE: entry.shipmentDate || "",
          CARRIERNAME: carrierName,
          INVOICESTATUS: entry.invoiceStatus || "",
          MANUALMATCHING: entry.manualMatching || "",
          INVOICETYPE: entry.invoiceType || "",
          BILLTOCLIENT: entry.billToClient || "",
          CURRENCY: entry.currency || "",
          QTYSHIPPED: entry.qtyShipped || "",
          WEIGHTUNITNAME: entry.weightUnitName || "",
          QUANTITYBILLEDTEXT: entry.quantityBilledText || "",
          FREIGHTCLASS: entry.freightClass || "",
          INVOICETOTAL: entry.invoiceTotal || "",
          SAVINGS: entry.savings || "",
          FINANCIALNOTES: entry.financialNotes || "",
          OTHERDOCUMENTS: entry.otherDocuments || "",
          NOTES: entry.notes || "",
        };
        // Replace placeholders in the pattern
        let filename = pattern;
        placeholders.forEach((placeholder) => {
          // Only replace if it's a defined field
          if (!isField(placeholder)) return;
          const value = replacements[placeholder];
          if (value) {
            if (placeholder === "RECEIVE DATE") {
              const regex = /RECEIVE\s+DATE(?=\/|\.|$)/gi;
              filename = filename.replace(regex, value);
            } else if (placeholder === "FTP FILE NAME" || placeholder === "ASSOCIATE" || placeholder === "ADDITIONALFOLDERNAME") {
              const regex = new RegExp(
                `{${placeholder}}|${placeholder}(?=\/|\\.|$)`,
                "g"
              );
              filename = filename.replace(regex, value);
            } else {
              const regex = new RegExp(
                `{${placeholder}}|${placeholder}(?=\/|\\.|$)`,
                "g"
              );
              filename = filename.replace(regex, value.toUpperCase());
            }
          }
        });

        const isValid = missing.length === 0;
        return { filename, isValid, missing };
      } catch (err) {
        console.error("Error generating filename:", err);
        return {
          filename: "",
          isValid: false,
          missing: ["Error generating filename"],
        };
      }
    },
    [clientFilePathFormat, associate, client, userData?.username, carrier]
  );

  const renderTooltipContent = (index: number) => {
    if (!clientFilePathFormat) {
      return (
        <div className="text-sm max-w-md">
          <p className="font-medium mb-1">Entry #{index + 1} Filename</p>
          <p className="font-medium text-orange-600 mb-2">
            Please select a client to generate filename
          </p>
        </div>
      );
    }

    const hasGeneratedFilename =
      generatedFilenames[index] && generatedFilenames[index].length > 0;
    const isValid = filenameValidation[index];

    return (
      <div className="text-sm max-w-md">
        <p className="font-medium mb-1">Entry #{index + 1} Filename</p>
        {hasGeneratedFilename && isValid ? (
          <div>
            <p className="font-medium text-green-600 mb-2">
              Filename Generated Successfully
            </p>
            <p className="text-xs font-mono break-all bg-gray-100 p-2 rounded text-black">
              {generatedFilenames[index]}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Pattern: {clientFilePathFormat}
            </p>
          </div>
        ) : (
          <div>
            <p className="font-medium text-orange-600 mb-1">
              {hasGeneratedFilename
                ? "Invalid Filename"
                : "Please fill the form to generate filename"}
            </p>
            {missingFields[index] && missingFields[index].length > 0 && (
              <>
                <p className="text-xs text-gray-600 mb-2">Missing fields:</p>
                <ul className="list-disc list-inside space-y-1">
                  {missingFields[index].map((field, fieldIndex) => (
                    <li key={fieldIndex} className="text-xs">
                      {field}
                    </li>
                  ))}
                </ul>
              </>
            )}
          </div>
        )}
      </div>
    );
  };

  const handleCompanyAutoPopulation = useCallback(
    (entryIndex: number, entryClientId: string) => {
      const entryClientName =
        clientOptions?.find((c: any) => c.value === entryClientId)?.name || "";
      const currentEntry = form.getValues(`entries.${entryIndex}`);

      if (entryClientName && entryClientName !== "LEGRAND") {
        form.setValue(`entries.${entryIndex}.company`, entryClientName);
      } else if (entryClientName === "LEGRAND") {
        const shipperAlias = (currentEntry as any).shipperAlias;
        const consigneeAlias = (currentEntry as any).consigneeAlias;
        const billtoAlias = (currentEntry as any).billtoAlias;
        const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;

        if (!hasAnyLegrandData && currentEntry.company !== "") {
          form.setValue(`entries.${entryIndex}.company`, "");
        }
      } else {
        if (currentEntry.company !== "") {
          form.setValue(`entries.${entryIndex}.company`, "");
        }
      }
    },
    [form, clientOptions]
  );

  const handleCustomFieldsFetch = useCallback(
    async (entryIndex: number, entryClientId: string) => {
      if (!entryClientId) {
        const currentCustomFields = form.getValues(
          `entries.${entryIndex}.customFields`
        );
        if (currentCustomFields && currentCustomFields.length > 0) {
          form.setValue(`entries.${entryIndex}.customFields`, []);
        }
        return;
      }

      const currentCustomFields =
        form.getValues(`entries.${entryIndex}.customFields`) || [];

      const hasEmptyAutoUsernameFields = currentCustomFields.some(
        (field: any) =>
          field.type === "AUTO" &&
          field.autoOption === "USERNAME" &&
          !field.value &&
          userData?.username
      );

      const shouldFetchCustomFields =
        currentCustomFields.length === 0 ||
        (currentCustomFields.length > 0 && !currentCustomFields[0]?.clientId) ||
        currentCustomFields[0]?.clientId !== entryClientId ||
        hasEmptyAutoUsernameFields;

      if (shouldFetchCustomFields) {
        const customFieldsData = await fetchCustomFieldsForClient(
          entryClientId
        );

        const fieldsWithClientId = customFieldsData.map((field: any) => ({
          ...field,
          clientId: entryClientId,
        }));

        form.setValue(`entries.${entryIndex}.customFields`, fieldsWithClientId);

        setTimeout(() => {
          fieldsWithClientId.forEach((field: any, fieldIndex: number) => {
            const fieldPath =
              `entries.${entryIndex}.customFields.${fieldIndex}.value` as any;
            if (field.value) {
              form.setValue(fieldPath, field.value);
            }
          });
          setCustomFieldsRefresh((prev) => prev + 1);
        }, 100);
      }
    },
    [form, fetchCustomFieldsForClient, userData?.username]
  );

  const updateFilenames = useCallback(() => {
    const formValues = form.getValues();
    const filenames: string[] = [];
    const validations: boolean[] = [];
    const missingArr: string[][] = [];

    if (formValues.entries && Array.isArray(formValues.entries)) {
      formValues.entries.forEach((_, index) => {
        const { filename, isValid, missing } = generateFilename(
          index,
          formValues
        );
        filenames[index] = filename;
        validations[index] = isValid;
        missingArr[index] = missing || [];
      });
    }
    setGeneratedFilenames(filenames);
    setFilenameValidation(validations);
    setMissingFields(missingArr);
  }, [form, generateFilename]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateFilenames();
    }, 100);
    return () => clearTimeout(timeoutId);
  }, [entries, updateFilenames]);

  const handleInitialSelection = useCallback(
    (_associateId: string, _clientId: string) => {
      form.setValue("associateId", associateId);
      form.setValue("clientId", clientId);

      setTimeout(() => {
        handleCompanyAutoPopulation(0, clientId);
        handleCustomFieldsFetch(0, clientId);
        updateFilenames();
      }, 50);

      setShowFullForm(true);
    },
    [form, handleCompanyAutoPopulation, handleCustomFieldsFetch, updateFilenames, associateId, clientId]
  );

  useEffect(() => {
    if (associateId && clientId) {
      setShowFullForm(true);
      handleInitialSelection(associateId, clientId);
    } else {
      setShowFullForm(false);
    }

  }, [associateId, clientId, handleInitialSelection]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateFilenames();
      const formValues = form.getValues();
      if (formValues.entries && Array.isArray(formValues.entries)) {
        formValues.entries.forEach((entry: any, index: number) => {
          const entryClientId =
            entry?.clientId || (index === 0 ? formValues.clientId : "");
          if (entryClientId) {
            //handleCustomFieldsFetch(index, entryClientId);
          }
        });
      }
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [updateFilenames, handleCustomFieldsFetch, form]);

  useEffect(() => {
    const subscription = form.watch((_, { name }) => {
      if (
        name &&
        (name.includes("associateId") ||
          name.includes("clientId") ||
          name.includes("carrierName") ||
          name.includes("invoiceDate") ||
          name.includes("receivedDate") ||
          name.includes("ftpFileName") ||
          name.includes("company") ||
          name.includes("division"))
      ) {
        const timeoutId = setTimeout(() => {
          updateFilenames();

          if (name.includes("division")) {
            const entryMatch = name.match(/entries\.(\d+)\.division/);
            if (entryMatch) {
              const entryIndex = parseInt(entryMatch[1], 10);
              const formValues = form.getValues();
              const entry = formValues.entries?.[entryIndex] as any;
              const entryClientId =
                entry?.clientId ||
                (entryIndex === 0 ? formValues.clientId : "");
              const entryClientName =
                clientOptions?.find((c: any) => c.value === entryClientId)
                  ?.name || "";

              if (entryClientName === "LEGRAND") {
                const divisionValue =
                  formValues.entries?.[entryIndex]?.division;
                if (divisionValue) {
                  handleManualMatchingAutoFill(entryIndex, divisionValue);
                }
              }
            }
          }

          if (name.includes("clientId")) {
            const entryMatch = name.match(/entries\.(\d+)\.clientId/);
            if (entryMatch) {
              const entryIndex = parseInt(entryMatch[1], 10);
              const formValues = form.getValues();
              const entry = formValues.entries?.[entryIndex] as any;
              const entryClientId =
                entry?.clientId || formValues.clientId || "";
              const entryClientName =
                clientOptions?.find((c: any) => c.value === entryClientId)
                  ?.name || "";

              if (entryClientName === "LEGRAND" && entry?.division) {
                handleManualMatchingAutoFill(entryIndex, entry.division);
              }
            }
          }
        }, 100);

        return () => clearTimeout(timeoutId);
      }
    });

    return () => subscription.unsubscribe();
  }, [updateFilenames, handleManualMatchingAutoFill, clientOptions, form]);

  const onSubmit = useCallback(
    async (values: FormValues) => {
      try {
        const errorsToApply: { field: string; message: string }[] = [];

        for (let i = 0; i < values.entries.length; i++) {
          form.clearErrors(`entries.${i}.invoice`);
          form.clearErrors(`entries.${i}.receivedDate`);
        }

        const validationPromises = values.entries.map(async (entry, index) => {
          let entryHasError = false;

          const invoiceExists = await checkInvoiceExistence(entry.invoice);
          if (invoiceExists) {
            errorsToApply.push({
              field: `entries.${index}.invoice`,
              message: "This invoice already exists",
            });

            if (entry.invoice && entry.receivedDate) {
              const receivedDateExists = await checkReceivedDateExistence(
                entry.invoice,
                entry.receivedDate
              );
              if (receivedDateExists) {
                errorsToApply.push({
                  field: `entries.${index}.receivedDate`,
                  message: "This received date already exists for this invoice",
                });
                entryHasError = true;
              } else {
                errorsToApply.push({
                  field: `entries.${index}.receivedDate`,
                  message:
                    "Warning: Different received date for existing invoice",
                });
              }
            }
          }
          return { isValid: !entryHasError };
        });

        await Promise.all(validationPromises);

        errorsToApply.forEach(({ field, message }) => {
          form.setError(field as any, { type: "manual", message });
        });

        const hasDuplicateReceivedDateErrors = errorsToApply.some(
          (error) =>
            error.field.includes("receivedDate") &&
            error.message.includes("already exists")
        );

        if (hasDuplicateReceivedDateErrors) {
          return;
        }

        const currentFormValues = form.getValues();
        const currentValidation: boolean[] = [];
        const currentMissingFields: string[][] = [];
        const currentFilenames: string[] = [];

        if (
          currentFormValues.entries &&
          Array.isArray(currentFormValues.entries)
        ) {
          currentFormValues.entries.forEach((_, index) => {
            const { filename, isValid, missing } = generateFilename(
              index,
              currentFormValues
            );
            currentValidation[index] = isValid;
            currentMissingFields[index] = missing || [];
            currentFilenames[index] = filename;
          });
        }

        // Remove validation check to allow submission
        const entries = values.entries.map((entry, index) => ({
          company: entry.company,
          division: entry.division,
          invoice: entry.invoice,
          masterInvoice: entry.masterInvoice,
          bol: entry.bol,
          invoiceDate: entry.invoiceDate,
          receivedDate: entry.receivedDate,
          shipmentDate: entry.shipmentDate,
          carrierId: entry.carrierName,
          invoiceStatus: entry.invoiceStatus,
          manualMatching: entry.manualMatching,
          invoiceType: entry.invoiceType,
          billToClient: entry.billToClient,
          currency: entry.currency,
          qtyShipped: entry.qtyShipped,
          weightUnitName: entry.weightUnitName,
          quantityBilledText: entry.quantityBilledText,
          freightClass: entry.freightClass,
          invoiceTotal: entry.invoiceTotal,
          savings: entry.savings,
          ftpFileName: entry.ftpFileName,
          ftpPage: entry.ftpPage,
          docAvailable: entry.docAvailable,
          notes: entry.notes,
          filePath: generatedFilenames[index],
          customFields: entry.customFields?.map((cf) => ({
            id: cf.id,
            value: cf.value,
          })),
          enteredBy: userData?.username || entry.enteredBy || "",
        }));
        const formData = {
          clientId: values.clientId,
          entries: entries,
        };

        const result = await formSubmit(
          trackSheets_routes.CREATE_TRACK_SHEETS,
          "POST",
          formData
        );

        if (result.success) {
          toast.success("All TrackSheets created successfully");
          // Get current values for carrierName and receivedDate
          const currentCarrier = form.getValues("entries.0.carrierName");
          const currentReceivedDate = form.getValues("entries.0.receivedDate");
          // Reset all fields except carrierName and receivedDate
          form.setValue("entries", [
            {
              company: "",
              division: "",
              invoice: "",
              masterInvoice: "",
              bol: "",
              invoiceDate: "",
              receivedDate: currentReceivedDate,
              shipmentDate: "",
              carrierName: currentCarrier,
              invoiceStatus: "ENTRY",
              manualMatching: "",
              invoiceType: "",
              billToClient: "yes",
              currency: "",
              qtyShipped: "",
              weightUnitName: "",
              quantityBilledText: "",
              freightClass: "",
              invoiceTotal: "",
              savings: "",
              financialNotes: "",
              ftpFileName: "",
              ftpPage: "",
              docAvailable: [],
              otherDocuments: "",
              notes: "",
              legrandAlias: "",
              legrandCompanyName: "",
              legrandAddress: "",
              legrandZipcode: "",
              shipperAlias: "",
              shipperAddress: "",
              shipperZipcode: "",
              consigneeAlias: "",
              consigneeAddress: "",
              consigneeZipcode: "",
              billtoAlias: "",
              billtoAddress: "",
              billtoZipcode: "",
              customFields: [],
              enteredBy: userData?.username || "",
            },
          ]);
          setTimeout(() => {
            handleInitialSelection(associateId, clientId);
          }, 100);
        } else {
          toast.error(result.message || "Failed to create TrackSheets");
        }
        router.refresh();
      } catch (error) {
        toast.error("An error occurred while creating the TrackSheets");
      }
    },
    [
      form,
      router,
      generateFilename,
      associateId,
      clientId,
      handleInitialSelection,
      generatedFilenames,
      userData?.username,
    ]
  );

  // const addNewEntry = useCallback(() => {
  //   const newIndex = fields.length;
  //   append({
  //     clientId: initialClientId,
  //     company: "",
  //     division: "",
  //     invoice: "",
  //     masterInvoice: "",
  //     bol: "",
  //     invoiceDate: "",
  //     receivedDate: "",
  //     shipmentDate: "",
  //     carrierName: "",
  //     invoiceStatus: "ENTRY",
  //     manualMatching: "",
  //     invoiceType: "",
  //     billToClient: "",
  //     currency: "",
  //     qtyShipped: "",
  //     weightUnitName: "",
  //     quantityBilledText: "",
  //     invoiceTotal: "",
  //     savings: "",
  //     financialNotes: "",
  //     ftpFileName: "",
  //     ftpPage: "",
  //     docAvailable: [],
  //     otherDocuments: "",
  //     notes: "",
  //     //mistake: "",
  //     legrandAlias: "",
  //     legrandCompanyName: "",
  //     legrandAddress: "",
  //     legrandZipcode: "",
  //     shipperAlias: "",
  //     shipperAddress: "",
  //     shipperZipcode: "",
  //     consigneeAlias: "",
  //     consigneeAddress: "",
  //     consigneeZipcode: "",
  //     billtoAlias: "",
  //     billtoAddress: "",
  //     billtoZipcode: "",
  //     customFields: [],
  //   } as any);

  //   setTimeout(() => {
  //     handleCompanyAutoPopulation(newIndex, initialClientId);
  //     handleCustomFieldsFetch(newIndex, initialClientId);

  //     if (companyFieldRefs.current[newIndex]) {
  //       const inputElement =
  //         companyFieldRefs.current[newIndex]?.querySelector("input") ||
  //         companyFieldRefs.current[newIndex]?.querySelector("button") ||
  //         companyFieldRefs.current[newIndex]?.querySelector("select");

  //       if (inputElement) {
  //         inputElement.focus();
  //         try {
  //           inputElement.click();
  //         } catch (e) {}
  //       }
  //     }
  //     updateFilenames();
  //   }, 200);
  // }, [
  //   append,
  //   fields.length,
  //   updateFilenames,
  //   initialClientId,
  //   handleCompanyAutoPopulation,
  //   handleCustomFieldsFetch,
  // ]);

  const handleFormKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.ctrlKey && (e.key === "s" || e.key === "S")) {
        e.preventDefault();
        form.handleSubmit(onSubmit)();
      }
      // else if (e.shiftKey && e.key === "Enter") {
      //   e.preventDefault();
      //   addNewEntry();
      // }
      else if (e.key === "Enter" && !e.ctrlKey && !e.shiftKey && !e.altKey) {
        const activeElement = document.activeElement;
        const isSubmitButton = activeElement?.getAttribute("type") === "submit";

        if (isSubmitButton) {
          e.preventDefault();
          form.handleSubmit(onSubmit)();
        }
      }
    },
    [form, onSubmit]
  );

  // const removeEntry = (index: number) => {
  //   if (fields.length > 1) {
  //     remove(index);
  //   } else {
  //     toast.error("You must have at least one entry");
  //   }
  // };

  const getFilteredDivisionOptions = (company: string, entryIndex?: number) => {
    if (entryIndex !== undefined) {
      const formValues = form.getValues();
      const entry = formValues.entries?.[entryIndex] as any;
      const entryClientId =
        entry?.clientId || (entryIndex === 0 ? formValues.clientId : "");
      const entryClientName =
        clientOptions?.find((c: any) => c.value === entryClientId)?.name || "";

      if (entryClientName === "LEGRAND") {
        const shipperAlias = form.getValues(
          `entries.${entryIndex}.shipperAlias`
        );
        const consigneeAlias = form.getValues(
          `entries.${entryIndex}.consigneeAlias`
        );
        const billtoAlias = form.getValues(`entries.${entryIndex}.billtoAlias`);

        const currentAlias = shipperAlias || consigneeAlias || billtoAlias;

        if (currentAlias) {
          const selectedData = legrandData.find((data) => {
            const uniqueKey = `${data.customeCode}-${
              data.aliasShippingNames || data.legalName
            }-${data.shippingBillingAddress}`;
            return uniqueKey === currentAlias;
          });

          if (selectedData) {
            const baseAliasName =
              selectedData.aliasShippingNames &&
              selectedData.aliasShippingNames !== "NONE"
                ? selectedData.aliasShippingNames
                : selectedData.legalName;

            const sameAliasEntries = legrandData.filter((data) => {
              const dataAliasName =
                data.aliasShippingNames && data.aliasShippingNames !== "NONE"
                  ? data.aliasShippingNames
                  : data.legalName;
              return dataAliasName === baseAliasName;
            });

            const allDivisions: string[] = [];
            sameAliasEntries.forEach((entry) => {
              if (entry.customeCode) {
                if (entry.customeCode.includes("/")) {
                  const splitDivisions = entry.customeCode
                    .split("/")
                    .map((d: string) => d.trim());
                  allDivisions.push(...splitDivisions);
                } else {
                  allDivisions.push(entry.customeCode);
                }
              }
            });

            const uniqueDivisions = Array.from(
              new Set(allDivisions.filter((code) => code))
            );

            // Always return unique divisions if any are found
            if (uniqueDivisions.length > 0) {
              const contextDivisions = uniqueDivisions.sort().map((code) => ({
                value: code,
                label: code,
              }));

              return contextDivisions;
            }
          }
        }
      }
    }

    // For non-LEGRAND clients or if no alias is selected for LEGRAND, return an empty array to allow free text input
    return [];
  };

  const handleOpenImportModal = () => {
    console.log("open import modal"); 
    setImportModalOpen(true);
  };

  const handleCloseImportModal = () => {
    console.log("close import modal");
    setImportModalOpen(false);
  };
  const checkInvoiceExistence = useCallback(async (invoice: string) => {
    if (!invoice || invoice.length < 3) return false;
    try {
      const response = await getAllData(
        `${trackSheets_routes.GET_RECEIVED_DATES_BY_INVOICE}?invoice=${invoice}`
      );

      return Array.isArray(response) && response.length > 0;
    } catch (error) {
      return false;
    }
  }, []);

  const checkReceivedDateExistence = useCallback(
    async (invoice: string, receivedDate: string) => {
      if (!invoice || !receivedDate || invoice.length < 3) return false;
      try {
        const response = await getAllData(
          `${trackSheets_routes.GET_RECEIVED_DATES_BY_INVOICE}?invoice=${invoice}`
        );
        if (Array.isArray(response) && response.length > 0) {
          const [day, month, year] = receivedDate.split("/");
          const inputDate = new Date(
            Date.UTC(
              parseInt(year, 10),
              parseInt(month, 10) - 1,
              parseInt(day, 10)
            )
          );
          const inputDateISO = inputDate.toISOString().split("T")[0];

          const exists = response.some((item: any) => {
            if (item.receivedDate) {
              const apiDate = new Date(item.receivedDate);
              const apiDateISO = apiDate.toISOString().split("T")[0];
              return apiDateISO === inputDateISO;
            }
            return false;
          });
          setExistingEntries((prev) => ({
            ...prev,
            [`${invoice}-${receivedDate}`]: exists,
          }));

          return exists;
        }
        return false;
      } catch (error) {
        console.error("Error checking received date:", error);
        return false;
      }
    },
    []
  );

  useEffect(() => {
    // Only fetch this data when needed, not on every mount
    // These APIs are only used for LEGRAND clients
    // We'll fetch them lazily when a LEGRAND client is selected
  }, []);

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="w-full px-2 py-3">
          {/* Content Section - Show based on active view */}
          {activeView === "view" ? (
            <div className="w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1">
              <ClientSelectPage
                permissions={permissions}
                client={client}
                clientDataUpdate={clientDataUpdate}
                carrierDataUpdate={carrierDataUpdate}
              />
            </div>
          ) : (
            /* Form Section - Only show when both associate and client are selected */
            showFullForm && (
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  onKeyDown={handleFormKeyDown}
                  className="space-y-3"
                >
                  {fields.map((field, index) => (
                    <div key={field.id} className="relative">
                      {/* Entry Header - Ultra Compact */}
                      <div className="flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200">
                        <div className="flex items-center space-x-2">
                          <div className="w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs">
                            {index + 1}
                          </div>
                          <h2 className="text-sm font-semibold text-gray-900">
                            Entry #{index + 1}
                          </h2>
                        </div>
                      </div>

                      {/* Main Form Content - Compact Layout */}
                      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
                        {/* Client Selection Row - For every entry */}
                        <div className="mb-3 pb-3 border-b border-gray-100">
                          {/* Client Selection & Company Information */}
                          <div className="bg-gray-50 rounded-md p-2 mb-3">
                            <div className="flex items-center space-x-2 mb-2">
                              <Building2 className="w-4 h-4 text-blue-600" />
                              <h3 className="text-sm font-semibold text-gray-900">
                                Client Information
                              </h3>
                            </div>
                            {/* Single Row - FTP File Name, FTP Page, Select Carrier, Billed to Radio */}
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3">
                              <div className="">
                                <FormInput
                                  className="mt-2"
                                  form={form}
                                  label="FTP File Name"
                                  name={`entries.${index}.ftpFileName`}
                                  type="text"
                                  isRequired
                                  onBlur={(e) =>
                                    handleFtpFileNameChange(
                                      index,
                                      e.target.value
                                    )
                                  }
                                />
                              </div>
                              <PageInput
                                className="mt-2"
                                form={form}
                                label="FTP Page"
                                name={`entries.${index}.ftpPage`}
                                isRequired
                              />
                              <div className="flex flex-col">
                                <SearchSelect
                                  className="mt-0"
                                  form={form}
                                  name={`entries.${index}.carrierName`}
                                  label="Select Carrier"
                                  placeholder="Search Carrier"
                                  isRequired
                                  options={carrierByClient || []}
                                  onValueChange={(value) => {
                                    setTimeout(() => updateFilenames(), 100);
                                  }}
                                />
                              </div>
                              <div className="flex flex-col">
                                {(() => {
                                  const formValues = form.getValues();
                                  const entry = formValues.entries?.[
                                    index
                                  ] as any;
                                  const entryClientId =
                                    entry?.clientId ||
                                    formValues.clientId ||
                                    "";
                                  const entryClientName =
                                    clientOptions?.find(
                                      (c: any) => c.value === entryClientId
                                    )?.name || "";

                                  return (
                                    <div className="mt-6">
                                      <label className="text-sm font-medium text-gray-700 mb-1 block">
                                        Billed to {entryClientName || "Client"}
                                      </label>
                                      <div className="flex items-center space-x-4">
                                        <label className="flex items-center">
                                          <input
                                            type="radio"
                                            {...form.register(
                                              `entries.${index}.billToClient`
                                            )}
                                            value="yes"
                                            defaultChecked={true}
                                            className="mr-2"
                                          />
                                          <span className="text-sm">Yes</span>
                                        </label>
                                        <label className="flex items-center">
                                          <input
                                            type="radio"
                                            {...form.register(
                                              `entries.${index}.billToClient`
                                            )}
                                            value="no"
                                            className="mr-2"
                                          />
                                          <span className="text-sm">No</span>
                                        </label>
                                      </div>
                                    </div>
                                  );
                                })()}
                              </div>
                            </div>

                            {/* LEGRAND Details and Radio Button - Show only for LEGRAND client */}
                            {(() => {
                              const formValues = form.getValues();
                              const entry = formValues.entries?.[index] as any;
                              const entryClientId =
                                entry?.clientId || formValues.clientId || "";
                              const entryClientName =
                                clientOptions?.find(
                                  (c: any) => c.value === entryClientId
                                )?.name || "";
                              return entryClientName === "LEGRAND";
                            })() && (
                              <div className="mb-3">
                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3">
                                  <LegrandDetailsComponent
                                    form={form}
                                    entryIndex={index}
                                    onLegrandDataChange={handleLegrandDataChange}
                                    blockTitle="Shipper"
                                    fieldPrefix="shipper"
                                    legrandData={legrandData}
                                  />
                                  <LegrandDetailsComponent
                                    form={form}
                                    entryIndex={index}
                                    onLegrandDataChange={handleLegrandDataChange}
                                    blockTitle="Consignee"
                                    fieldPrefix="consignee"
                                    legrandData={legrandData}
                                  />
                                  <LegrandDetailsComponent
                                    form={form}
                                    entryIndex={index}
                                    onLegrandDataChange={handleLegrandDataChange}
                                    blockTitle="Bill-to"
                                    fieldPrefix="billto"
                                    legrandData={legrandData}
                                  />
                                </div>
                              </div>
                            )}

                            {/* Third Row - Company, Division and Manual or Matching */}
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-3">
                              <div className="flex-1">
                                <FormInput
                                  className="mt-2"
                                  form={form}
                                  label="Company"
                                  name={`entries.${index}.company`}
                                  placeholder="Enter Company Name"
                                  isRequired
                                  type="text"
                                  disable={(() => {
                                    const formValues = form.getValues();
                                    const entry = formValues.entries?.[
                                      index
                                    ] as any;
                                    const entryClientId =
                                      entry?.clientId ||
                                      formValues.clientId ||
                                      "";
                                    const entryClientName =
                                      clientOptions?.find(
                                        (c: any) => c.value === entryClientId
                                      )?.name || "";
                                    return entryClientName === "LEGRAND";
                                  })()}
                                />
                              </div>
                              {/* Division field: SearchSelect for LEGRAND, FormInput for others */}
                              {(() => {
                                const formValues = form.getValues();
                                const entry = formValues.entries?.[
                                  index
                                ] as any;
                                const entryClientId =
                                  entry?.clientId || formValues.clientId || "";
                                const entryClientName =
                                  clientOptions?.find(
                                    (c: any) => c.value === entryClientId
                                  )?.name || "";
                                return entryClientName === "LEGRAND";
                              })() ? (
                                <SearchSelect
                                  form={form}
                                  name={`entries.${index}.division`}
                                  label="Division"
                                  placeholder="Select Division..."
                                  options={getFilteredDivisionOptions(
                                    form.getValues(`entries.${index}.company`),
                                    index
                                  ).map((option) => ({
                                    value: option.value,
                                    label: option.label,
                                  }))}
                                  onValueChange={(value) => {
                                    // Handle division change
                                  }}
                                />
                              ) : (
                                <FormInput
                                  className="mt-2"
                                  form={form}
                                  name={`entries.${index}.division`}
                                  label="Division"
                                  placeholder="Enter Division"
                                  type="text"
                                />
                              )}

                              <FormInput
                                className="mt-2"
                                form={form}
                                label="Manual or Matching"
                                name={`entries.${index}.manualMatching`}
                                type="text"
                                isRequired
                                disable={(() => {
                                  const formValues = form.getValues();
                                  const entry = formValues.entries?.[
                                    index
                                  ] as any;
                                  const entryClientId =
                                    entry?.clientId ||
                                    formValues.clientId ||
                                    "";
                                  const entryClientName =
                                    clientOptions?.find(
                                      (c: any) => c.value === entryClientId
                                    )?.name || "";
                                  return entryClientName === "LEGRAND";
                                })()}
                                placeholder={
                                  (() => {
                                    const formValues = form.getValues();
                                    const entry = formValues.entries?.[
                                      index
                                    ] as any;
                                    const entryClientId =
                                      entry?.clientId ||
                                      formValues.clientId ||
                                      "";
                                    const entryClientName =
                                      clientOptions?.find(
                                        (c: any) => c.value === entryClientId
                                      )?.name || "";
                                    return entryClientName === "LEGRAND";
                                  })()
                                    ? "Auto-filled based on division"
                                    : "Enter manual or matching"
                                }
                              />
                            </div>
                          </div>
                        </div>

                        {/* Document Information */}
                        <div className="mb-3 pb-3 border-b border-gray-100">
                          <div className="flex items-center space-x-2 mb-2">
                            <FileText className="w-4 h-4 text-orange-600" />
                            <h3 className="text-sm font-semibold text-gray-900">
                              Document Information
                            </h3>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                            <FormInput
                              form={form}
                              label="Master Invoice"
                              name={`entries.${index}.masterInvoice`}
                              type="text"
                              onBlur={(e) => {
                                const masterInvoiceValue = e.target.value;
                                if (masterInvoiceValue) {
                                  form.setValue(
                                    `entries.${index}.invoice`,
                                    masterInvoiceValue
                                  );
                                }
                              }}
                            />
                            <FormInput
                              form={form}
                              label="Invoice"
                              name={`entries.${index}.invoice`}
                              type="text"
                              isRequired
                              onBlur={async (e) => {
                                const invoiceValue = e.target.value;
                                const receivedDate = form.getValues(
                                  `entries.${index}.receivedDate`
                                );

                                // Clear previous errors
                                form.clearErrors(`entries.${index}.invoice`);
                                form.clearErrors(
                                  `entries.${index}.receivedDate`
                                );

                                if (invoiceValue && invoiceValue.length >= 3) {
                                  const exists = await checkInvoiceExistence(
                                    invoiceValue
                                  );
                                  if (exists) {
                                    form.setError(`entries.${index}.invoice`, {
                                      type: "manual",
                                      message: "This invoice already exists",
                                    });

                                    if (receivedDate) {
                                      const receivedDateExists =
                                        await checkReceivedDateExistence(
                                          invoiceValue,
                                          receivedDate
                                        );
                                      if (receivedDateExists) {
                                        form.setError(
                                          `entries.${index}.receivedDate`,
                                          {
                                            type: "manual",
                                            message:
                                              "This received date already exists for this invoice",
                                          }
                                        );
                                      } else {
                                        form.setError(
                                          `entries.${index}.receivedDate`,
                                          {
                                            type: "manual",
                                            message:
                                              "Warning: Different received date for existing invoice",
                                          }
                                        );
                                      }
                                    }
                                  }
                                }
                              }}
                            />
                            <FormInput
                              form={form}
                              label="BOL"
                              name={`entries.${index}.bol`}
                              type="text"
                            />
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                            <FormDatePicker
                              form={form}
                              label="Received Date"
                              name={`entries.${index}.receivedDate`}
                              isRequired
                              placeholder="DD/MM/YYYY"
                              onValueChange={async (value) => {
                                form.clearErrors(`entries.${index}.receivedDate`); // Clear errors at the start
                                handleDateChange(index, value); // First function

                                const invoice = form.getValues(
                                  `entries.${index}.invoice`
                                );
                                if (value && invoice && invoice.length >= 3) {
                                  const invoiceExists =
                                    await checkInvoiceExistence(invoice);
                                  if (invoiceExists) {
                                    const receivedDateExists =
                                      await checkReceivedDateExistence(
                                        invoice,
                                        value
                                      );
                                    if (receivedDateExists) {
                                      form.setError(
                                        `entries.${index}.receivedDate`,
                                        {
                                          type: "manual",
                                          message:
                                            "This received date already exists for this invoice",
                                        }
                                      );
                                    } else {
                                      form.setError(
                                        `entries.${index}.receivedDate`,
                                        {
                                          type: "manual",
                                          message:
                                            "Warning: Different received date for existing invoice",
                                        }
                                      );
                                    }
                                  }
                                }
                                // Immediate validation: Invoice Date should not be after Received Date (set error on Invoice Date field)
                                const invoiceDate = form.getValues(`entries.${index}.invoiceDate`);
                                form.clearErrors(`entries.${index}.invoiceDate`);
                                if (invoiceDate && value) {
                                  if (validateDateFormat(invoiceDate) && validateDateFormat(value)) {
                                    const [invDay, invMonth, invYear] = invoiceDate.split("/").map(Number);
                                    const [recDay, recMonth, recYear] = value.split("/").map(Number);
                                    const invoiceDateObj = new Date(invYear, invMonth - 1, invDay);
                                    const receivedDateObj = new Date(recYear, recMonth - 1, recDay);
                                    if (invoiceDateObj > receivedDateObj) {
                                      form.setError(`entries.${index}.invoiceDate`, {
                                        type: "manual",
                                        message: "The invoice date should be older than or the same as the received date.",
                                      });
                                    }
                                  }
                                }
                              }}
                            />
                            <FormDatePicker
                              form={form}
                              label="Invoice Date"
                              name={`entries.${index}.invoiceDate`}
                              isRequired
                              placeholder="DD/MM/YYYY"
                              disable={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                              onValueChange={async (value) => {
                                // Immediate validation: Invoice Date should not be after Received Date
                                const receivedDate = form.getValues(`entries.${index}.receivedDate`);
                                form.clearErrors(`entries.${index}.invoiceDate`);
                                if (value && receivedDate) {
                                  if (validateDateFormat(value) && validateDateFormat(receivedDate)) {
                                    const [invDay, invMonth, invYear] = value.split("/").map(Number);
                                    const [recDay, recMonth, recYear] = receivedDate.split("/").map(Number);
                                    const invoiceDateObj = new Date(invYear, invMonth - 1, invDay);
                                    const receivedDateObj = new Date(recYear, recMonth - 1, recDay);
                                    if (invoiceDateObj > receivedDateObj) {
                                      form.setError(`entries.${index}.invoiceDate`, {
                                        type: "manual",
                                        message: "The invoice date should be older than or the same as the received date.",
                                      });
                                    }
                                  }
                                }
                              }}
                            />
                            <FormDatePicker
                              form={form}
                              label="Shipment Date"
                              name={`entries.${index}.shipmentDate`}
                              placeholder="DD/MM/YYYY"
                              disable={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                            />
                          </div>
                        </div>

                        {/* Financial & Shipment Information */}
                        <div className="mb-4 pb-4 border-b border-gray-100">
                          <div className="flex items-center space-x-2 mb-3">
                            <DollarSign className="w-4 h-4 text-green-600" />
                            <h3 className="text-sm font-semibold text-gray-900">
                              Financial & Shipment
                            </h3>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                            <FormInput
                              className="mt-2"
                              form={form}
                              label="Invoice Total"
                              name={`entries.${index}.invoiceTotal`}
                              type="number"
                              isRequired
                              disable={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                            />
                            <SearchSelect
                              form={form}
                              name={`entries.${index}.currency`}
                              label="Currency"
                              placeholder="Search currency"
                              isRequired
                              disabled={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                              options={[
                                { value: "USD", label: "USD" },
                                { value: "CAD", label: "CAD" },
                                { value: "EUR", label: "EUR" },
                              ]}
                            />
                            <FormInput
                              className="mt-2"
                              form={form}
                              label="Savings"
                              name={`entries.${index}.savings`}
                              type="text"
                              disable={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                            />
                            <FormInput
                              className="mt-2"
                              form={form}
                              label="Notes"
                              name={`entries.${index}.financialNotes`}
                              type="text"
                              disable={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                            />
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2">
                            <FormInput
                              form={form}
                              label="Freight Class"
                              name={`entries.${index}.freightClass`}
                              type="text"
                              isRequired={(() => {
                                const formValues = form.getValues();
                                const entry = formValues.entries?.[
                                  index
                                ] as any;
                                const entryClientId =
                                  entry?.clientId || formValues.clientId || "";
                                const entryClientName =
                                  clientOptions?.find(
                                    (c: any) => c.value === entryClientId
                                  )?.name || "";
                                return entryClientName === "LEGRAND";
                              })()}
                              disable={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                            />
                            <FormInput
                              form={form}
                              label="Weight Unit"
                              name={`entries.${index}.weightUnitName`}
                              type="text"
                              disable={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                            />
                            <FormInput
                              form={form}
                              label="Quantity Billed"
                              name={`entries.${index}.quantityBilledText`}
                              type="text"
                              disable={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                            />
                            <FormInput
                              form={form}
                              label="Quantity Shipped"
                              name={`entries.${index}.qtyShipped`}
                              type="number"
                              disable={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                            />
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2">
                            <SearchSelect
                              form={form}
                              name={`entries.${index}.invoiceType`}
                              label="Invoice Type"
                              placeholder="Search Invoice Type"
                              isRequired
                              disabled={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                              options={[
                                { value: "FREIGHT", label: "FREIGHT" },
                                { value: "ADDITIONAL", label: "ADDITIONAL" },
                                {
                                  value: "BALANCED DUE",
                                  label: "BALANCED DUE",
                                },
                                { value: "CREDIT", label: "CREDIT" },
                                { value: "REVISED", label: "REVISED" },
                              ]}
                            />
                            <FormInput
                              className="mt-2"
                              form={form}
                              label="Invoice Status"
                              name={`entries.${index}.invoiceStatus`}
                              type="text"
                              disable={true}
                            />
                            <div></div>
                            <div></div>
                          </div>
                        </div>

                        {/* Additional Information & Documents */}
                        <div className="mb-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <Info className="w-4 h-4 text-gray-600" />
                            <h3 className="text-sm font-semibold text-gray-900">
                              Additional Information
                            </h3>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                            <FormInput
                              form={form}
                              label="Notes (Remarks)"
                              name={`entries.${index}.notes`}
                              type="text"
                              disable={
                                existingEntries[
                                  `${form.getValues(
                                    `entries.${index}.invoice`
                                  )}-${form.getValues(
                                    `entries.${index}.receivedDate`
                                  )}`
                                ]
                              }
                            />
                            <div>
                              <FormCheckboxGroup
                                form={form}
                                label="Documents Available"
                                name={`entries.${index}.docAvailable`}
                                options={[
                                  { label: "Invoice", value: "Invoice" },
                                  { label: "BOL", value: "Bol" },
                                  { label: "POD", value: "Pod" },
                                  {
                                    label: "Packages List",
                                    value: "Packages List",
                                  },
                                  {
                                    label: "Other Documents",
                                    value: "Other Documents",
                                  },
                                ]}
                                className="flex-row gap-2 text-xs"
                                disable={
                                  existingEntries[
                                    `${form.getValues(
                                      `entries.${index}.invoice`
                                    )}-${form.getValues(
                                      `entries.${index}.receivedDate`
                                    )}`
                                  ]
                                }
                              />
                            </div>
                            {/* Conditional input field for Other Documents - appears next to Documents Available */}
                            {(() => {
                              const formValues = form.getValues();
                              const entry = formValues.entries?.[index] as any;
                              const docAvailable = entry?.docAvailable || [];
                              const hasOtherDocuments =
                                docAvailable.includes("Other Documents");

                              return hasOtherDocuments ? (
                                <FormInput
                                  form={form}
                                  label="Specify Other Documents"
                                  name={`entries.${index}.otherDocuments`}
                                  type="text"
                                  isRequired
                                  placeholder="Enter other document types..."
                                />
                              ) : (
                                <div></div>
                              );
                            })()}
                            {/* Commented out Mistake field */}
                            {/* <FormInput
                              form={form}
                              label="Mistake"
                              name={`entries.${index}.mistake`}
                              type="text"
                            /> */}
                          </div>
                        </div>

                        {/* Custom Fields Section */}
                        {(() => {
                          const formValues = form.getValues();
                          const entry = formValues.entries?.[index] as any;
                          const customFields = entry?.customFields || [];

                          return Array.isArray(customFields) &&
                            customFields.length > 0 ? (
                            <div
                              key={`custom-fields-${index}-${customFieldsRefresh}`}
                              className="pt-3 border-t border-gray-100"
                            >
                              <div className="flex items-center space-x-2 mb-2">
                                <Hash className="w-4 h-4 text-purple-600" />
                                <h3 className="text-sm font-semibold text-gray-900">
                                  Custom Fields ({customFields.length})
                                </h3>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                                {customFields.map((cf: any, cfIdx: number) => {
                                  const fieldType = cf.type || "TEXT";
                                  const isAutoField = fieldType === "AUTO";
                                  const autoOption = cf.autoOption;

                                  const isDateField =
                                    fieldType === "DATE" ||
                                    (isAutoField && autoOption === "DATE");

                                  let inputType = "text";
                                  if (isDateField) {
                                    inputType = "date";
                                  } else if (fieldType === "NUMBER") {
                                    inputType = "number";
                                  }

                                  const fieldLabel = isAutoField
                                    ? `${cf.name} (Auto - ${autoOption})`
                                    : cf.name;

                                  return isDateField ? (
                                    <FormDatePicker
                                      key={cf.id}
                                      form={form}
                                      label={fieldLabel}
                                      name={`entries.${index}.customFields.${cfIdx}.value`}
                                      className="w-full"
                                      disable={
                                        isAutoField ||
                                        existingEntries[
                                          `${form.getValues(
                                            `entries.${index}.invoice`
                                          )}-${form.getValues(
                                            `entries.${index}.receivedDate`
                                          )}`
                                        ]
                                      }
                                      placeholder="DD/MM/YYYY"
                                    />
                                  ) : (
                                    <FormInput
                                      key={cf.id}
                                      form={form}
                                      label={fieldLabel}
                                      name={`entries.${index}.customFields.${cfIdx}.value`}
                                      type={inputType}
                                      className="w-full"
                                      disable={
                                        isAutoField ||
                                        existingEntries[
                                          `${form.getValues(
                                            `entries.${index}.invoice`
                                          )}-${form.getValues(
                                            `entries.${index}.receivedDate`
                                          )}`
                                        ]
                                      }
                                    />
                                  );
                                })}
                              </div>
                            </div>
                          ) : null;
                        })()}

                        {/* Entry Actions - Moved to bottom */}
                        <div className="pt-3 border-t border-gray-100 mt-3">
                          <div className="flex items-center justify-end space-x-2">
                            {/* Filename Status Tooltip */}
                            <Tooltip>
                              <TooltipTrigger asChild tabIndex={-1}>
                                <div
                                  className={`w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 ${
                                    !clientFilePathFormat
                                      ? "bg-gray-400 hover:bg-gray-500"
                                      : generatedFilenames[index] &&
                                        filenameValidation[index]
                                      ? "bg-green-500 hover:bg-green-600"
                                      : "bg-orange-500 hover:bg-orange-600"
                                  }`}
                                  tabIndex={-1}
                                  role="button"
                                  aria-label={`Entry ${
                                    index + 1
                                  } filename status`}
                                >
                                  !
                                </div>
                              </TooltipTrigger>
                              <TooltipContent
                                side="top"
                                align="center"
                                className="z-[9999]"
                              >
                                {renderTooltipContent(index)}
                              </TooltipContent>
                            </Tooltip>

                            {/* Commented out + - buttons for extra entries functionality */}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Submit Section - Compact */}
                  <div className="mt-3">
                    <div className="flex justify-center">
                      <Button
                        type="submit"
                        className="px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm"
                        disabled={
                          existingEntries[
                            `${form.getValues(
                              `entries.0.invoice`
                            )}-${form.getValues(`entries.0.receivedDate`)}`
                          ]
                        }
                      >
                        Save
                      </Button>
                    </div>
                  </div>
                </form>
              </Form>
            )
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

export default CreateTrackSheet;
