import { getAllData, getCookie } from "@/lib/helpers";
import {
  carrier_routes,
  client_routes,
  employee_routes,
  associate_routes,
} from "@/lib/routePath";
import ManageWorkSheet from "./ManageTrackSheet";

const Page = async () => {
  const client = await getAllData(
    `${client_routes.GETALL_CLIENT}?minimal=true`
  );
  const clientData = client?.data;
  const updateCarrier = await getAllData(
    `${carrier_routes.GETALL_CARRIER}?notIncludeWorkReport=true`
  );
  const carrierDataUpdate = updateCarrier?.data;
    const clientUpdate = await getAllData(
    `${client_routes.GETALL_CLIENT}?minimal=true`
  );
  const clientDataUpdate = clientUpdate?.data;

  const carriers = await getAllData(
    `${carrier_routes.GETALL_CARRIER}?notIncludeWorkReport=true`
  );
  const carrier = carriers?.data;

  const associate = await getAllData(associate_routes.GETALL_ASSOCIATE);
  const associateData = associate?.data;

  const userData = await getAllData(
    `${employee_routes.GETCURRENT_USER}?TracksheetUser=true`
  );

  console.timeEnd("Time taken for: " + employee_routes.GETCURRENT_USER);
  const userPermissions = userData?.role?.role_permission || [];

  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  const actions = permissions?.map((item) => item?.permission?.action);

  return (
    <div className="w-full">
      <ManageWorkSheet
        client={clientData}
        permissions={permissions}
        actions={actions}
        userData={userData}
        associate={associateData}
        carrierDataUpdate={carrierDataUpdate}
        clientDataUpdate={clientDataUpdate}
        carrier={carrier}
      />
    </div>
  );
};

export default Page;
