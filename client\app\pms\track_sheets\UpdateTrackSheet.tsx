import DialogHeading from "@/app/_component/DialogHeading";
import FormCheckboxGroup from "@/app/_component/FormCheckboxGroup";
import FormInput from "@/app/_component/FormInput";
import PageInput from "@/app/_component/PageInput";
import SearchSelect from "@/app/_component/SearchSelect";
import SelectComp from "@/app/_component/SelectComp";
import TriggerButton from "@/app/_component/TriggerButton";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog";
import { SelectItem } from "@/components/ui/select";
import useDynamicForm from "@/lib/useDynamicForm";
import { useRouter } from "next/navigation";
import { useState, useContext, useCallback } from "react";
import { toast } from "sonner";
import { z } from "zod";
import { FormProvider } from "react-hook-form";
import { trackSheets_routes, clientCustomFields_routes, customFilepath_routes, carrier_routes } from "@/lib/routePath";
import { formSubmit, getAllData } from "@/lib/helpers";
import { TrackSheetContext } from "./TrackSheetContext";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const baseTrackSheetSchema = {
  clientId: z.string().optional(),
  company: z.string().optional(),
  division: z.string().optional(),
  invoice: z.string().optional(),
  masterInvoice: z.string().optional(),
  bol: z.string().optional(),
  invoiceDate: z.string().optional(),
  receivedDate: z.string().optional(),
  shipmentDate: z.string().optional(),
  carrierId: z.string().optional(),
  invoiceStatus: z.string().optional(),
  manualMatching: z.string().optional(),
  freightClass: z.string().optional(),
  invoiceType: z.string().optional(),
  currency: z.string().optional(),
  qtyShipped: z.string().optional(),
  weightUnitName: z.string().optional(),
  quantityBilledText: z.string().optional(),
  invoiceTotal: z.string().optional(),
  savings: z.string().optional(),
  ftpFileName: z.string().optional(),
  ftpPage: z.string().optional(),
  docAvailable: z.array(z.string()).optional(),
  notes: z.string().optional(),
  mistake: z.string().optional(),
};

const FIELD_OPTIONS = [
  "ASSOCIATE",
  "CLIENT",
  "ADDITIONALFOLDERNAME",
  "CARRIER",
  "YEAR",
  "MONTH",
  "RECEIVE DATE",
  "FTP FILE NAME",
  "FTPPAGE",
  "ENTEREDBY",
  "COMPANY",
  "DIVISION",
  "INVOICE",
  "MASTERINVOICE",
  "BOL",
  "INVOICEDATE",
  "SHIPMENTDATE",
  "CARRIERNAME",
  "INVOICESTATUS",
  "MANUALMATCHING",
  "INVOICETYPE",
  "CURRENCY",
  "QTYSHIPPED",
  "WEIGHTUNITNAME",
  "QUANTITYBILLEDTEXT",
  "FREIGHTCLASS",
  "INVOICETOTAL",
  "SAVINGS",
  "NOTES"
];

const isField = (value: string) => FIELD_OPTIONS.includes(value.toUpperCase());

const UpdateTrackSheet = ({
  trackSheet,
  clientDataUpdate,
  carrierDataUpdate,
}: any) => {
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const [customFieldsForClient, setCustomFieldsForClient] = useState<any[]>([]);
  const [customFieldsLoading, setCustomFieldsLoading] = useState(false);
  const [clientFilePathFormat, setClientFilePathFormat] = useState<string | null>(null);
  const [generatedFilePath, setGeneratedFilePath] = useState<string>("");

  const [computedFilePath, setComputedFilePath] = useState<string>(
    trackSheet?.filePath || ""
  );
  const parseCustomFields = () => {
    try {
      if (!trackSheet?.customFields) {
        return {};
      }

      if (
        typeof trackSheet.customFields === "object" &&
        !Array.isArray(trackSheet.customFields)
      ) {
        return trackSheet.customFields;
      }

      if (typeof trackSheet.customFields === "string") {
        try {
          return JSON.parse(trackSheet.customFields);
        } catch (e) {
          console.error("Error parsing custom fields string:", e);
          return {};
        }
      }

      return {};
    } catch (error) {
      console.error("Error parsing custom fields:", error);
      return {};
    }
  };

  const parsedCustomFields = parseCustomFields();

  const createDynamicSchema = () => {
    const schema = { ...baseTrackSheetSchema };
    if (customFieldsForClient && Array.isArray(customFieldsForClient)) {
      customFieldsForClient.forEach((field: any) => {
        const fieldName = `custom_${field.name}`;
        schema[fieldName] = field.required
          ? z.string().min(1, `${field.label} is required`)
          : z.string().optional();
      });
    }
    return z.object(schema);
  };
  const { setCustomFieldsReloadTrigger } = useContext(TrackSheetContext);

  const initialValues = {
    clientId: trackSheet?.client?.id?.toString() || "",
    company: trackSheet?.company || "",
    division: trackSheet?.division || "",
    invoice: trackSheet?.invoice?.toString() || "",
    masterInvoice: trackSheet?.masterInvoice?.toString() || "",
    bol: trackSheet?.bol?.toString() || "",
    invoiceDate: trackSheet?.invoiceDate?.split("T")[0] || "",
    receivedDate: trackSheet?.receivedDate?.split("T")[0] || "",
    shipmentDate: trackSheet?.shipmentDate?.split("T")[0] || "",
    carrierId: trackSheet?.carrier?.id?.toString() || "",
    invoiceStatus: trackSheet?.invoiceStatus || "",
    manualMatching: trackSheet?.manualMatching || "",
    freightClass: trackSheet?.freightClass || "",
    invoiceType: trackSheet?.invoiceType || "",
    currency: trackSheet?.currency || "",
    qtyShipped: trackSheet?.qtyShipped?.toString() || "",
    weightUnitName: trackSheet?.weightUnitName || "",
    quantityBilledText: trackSheet?.quantityBilledText || "",
    invoiceTotal: trackSheet?.invoiceTotal?.toString() || "",
    savings: trackSheet?.savings || "",
    ftpFileName: trackSheet?.ftpFileName || "",
    ftpPage: trackSheet?.ftpPage || "",
    docAvailable: (() => {
      if (!trackSheet?.docAvailable) return [];
      if (Array.isArray(trackSheet.docAvailable))
        return trackSheet.docAvailable;
      try {
        const parsed = JSON.parse(trackSheet.docAvailable);
        return Array.isArray(parsed) ? parsed : [];
      } catch {
        return trackSheet.docAvailable.split(",").map((item) => item.trim());
      }
    })(),
    notes: trackSheet?.notes || "",
    mistake: trackSheet?.mistake || "",
  };

  if (customFieldsForClient && Array.isArray(customFieldsForClient)) {
    customFieldsForClient.forEach((field: any) => {
      const fieldName = `custom_${field.name}`;
      initialValues[fieldName] = field.value || "";
    });
  }

  const trackSheetSchema = createDynamicSchema();
  const { form } = useDynamicForm(trackSheetSchema, initialValues);

  const generateFilePath = useCallback((values: any) => {
    try {
      if (!clientFilePathFormat) {
        return { filename: "", isValid: false, missing: ["No client selected"] };
      }

      const pattern = clientFilePathFormat;
      let month = new Date().toLocaleString("default", { month: "long" }).toUpperCase();
      
      let formattedReceivedDate = "";
      if (values.receivedDate) {
        try {
          const [year, monthNum, day] = values.receivedDate.split("-");
          if (day && monthNum && year) {
            const receivedDate = new Date(
              parseInt(year),
              parseInt(monthNum) - 1,
              parseInt(day)
            );
            month = receivedDate.toLocaleString("default", { month: "long" }).toUpperCase();
            formattedReceivedDate = `${year}-${monthNum.padStart(2, "0")}-${day.padStart(2, "0")}`;
          }
        } catch (error) {
          console.error("Error formatting received date:", error);
        }
      }

      // Get client data including associate
      const selectedClient = clientDataUpdate?.find(
        (c: any) => c.id?.toString() === values.clientId
      );
      const clientName = selectedClient?.client_name || "";
      
      // Get associate name from the selected client's associate data
      const associateName = selectedClient?.associate?.name || "";

      const selectedCarrier = carrierDataUpdate?.find(
        (c: any) => c.id?.toString() === values.carrierId
      );
      const carrierName = selectedCarrier?.carrier_2nd_name || "";

      let formattedFtpFileName = values.ftpFileName || "";
      if (formattedFtpFileName) {
        formattedFtpFileName = formattedFtpFileName.replace(/\.pdf$/i, "");
      }

      const replacements: { [key: string]: string } = {
        CLIENT: clientName,
        CARRIER: carrierName,
        "FTP FILE NAME": formattedFtpFileName || "untitled",
        FTPPAGE: values.ftpPage || "",
        ENTEREDBY: values.enteredBy || "",
        YEAR: new Date().getFullYear().toString(),
        MONTH: month,
        "RECEIVE DATE": formattedReceivedDate,
        ASSOCIATE: associateName,
        COMPANY: values.company || "",
        DIVISION: values.division || "",
        INVOICE: values.invoice || "",
        MASTERINVOICE: values.masterInvoice || "",
        BOL: values.bol || "",
        INVOICEDATE: values.invoiceDate || "",
        SHIPMENTDATE: values.shipmentDate || "",
        CARRIERNAME: carrierName,
        INVOICESTATUS: values.invoiceStatus || "",
        MANUALMATCHING: values.manualMatching || "",
        INVOICETYPE: values.invoiceType || "",
        CURRENCY: values.currency || "",
        QTYSHIPPED: values.qtyShipped || "",
        WEIGHTUNITNAME: values.weightUnitName || "",
        QUANTITYBILLEDTEXT: values.quantityBilledText || "",
        FREIGHTCLASS: values.freightClass || "",
        INVOICETOTAL: values.invoiceTotal || "",
        SAVINGS: values.savings || "",
        NOTES: values.notes || "",
      };

      let filename = pattern
        .split('/')
        .map(segment => {
          // Remove curly braces if present and .pdf extension
          const clean = segment.replace(/[{}]/g, '').replace(/\.pdf$/i, '').trim();
          if (isField(clean)) {
            const value = replacements[clean];
            if (value) {
              if (clean === "RECEIVE DATE") return value;
              if (clean === "FTP FILE NAME") return value;
              if (clean === "ASSOCIATE" || clean === "ADDITIONALFOLDERNAME") return value;
              return value.toUpperCase();
            }
            return clean; // Return the field name if no value is found
          }
          // If not a field, return as static
          return segment;
        })
        .join('/');

      // Add .pdf extension if not present
      if (!filename.endsWith('.pdf')) {
        filename = `${filename}.pdf`;
      }

      return { filename, isValid: true, missing: [] };
    } catch (err) {
      console.error("Error generating file path:", err);
      return { filename: "", isValid: false, missing: ["Error generating file path"] };
    }
  }, [clientFilePathFormat, clientDataUpdate, carrierDataUpdate]);

  const [carrierByClient, setCarrierByClient] = useState([]);

  const handleChange = async (id: string) => {
    try {
      const carrierByClientData = await getAllData(
        `${carrier_routes.GET_CARRIER_BY_CLIENT}/${id}`
      );
      if (carrierByClientData && Array.isArray(carrierByClientData)) {
        const formattedCarriers = carrierByClientData.map((item: any) => ({
          value: item.carrier?.id?.toString(),
          label: item.carrier?.name,
        })).filter((carrier: any) => carrier.value && carrier.label);
        formattedCarriers.sort((a: any, b: any) => a.label.localeCompare(b.label));
        setCarrierByClient(formattedCarriers);
      } else {
        setCarrierByClient([]);
      }
    } catch (error) {
      console.error("Error fetching carrier data:", error);
      setCarrierByClient([]);
    }
  };

  // Watch form values for file path generation
  form.watch((value) => {
    const { filename } = generateFilePath(value);
    setGeneratedFilePath(filename);
  });

  // Dialog open handler triggers all necessary data fetching
  const handleDialogOpenChange = async (isOpen: boolean) => {
    setOpen(isOpen);
    if (isOpen && trackSheet?.client?.id) {
      // Fetch carriers for the client
      handleChange(trackSheet.client.id);

      // Fetch client file path format
      try {
        const res = await getAllData(`${customFilepath_routes.GET_CLIENT_CUSTOM_FILEPATH}?clientId=${trackSheet.client.id}`);
        if (res?.success && res?.data && Array.isArray(res.data) && res.data.length > 0) {
          const filepathData = res.data[0];
          if (filepathData && filepathData.filePath) {
            setClientFilePathFormat(filepathData.filePath);
          }
        }
      } catch (error) {
        console.error("Error fetching client file path format:", error);
      }

      // Fetch custom fields for client
      setCustomFieldsLoading(true);
      try {
        const res = await getAllData(
          `${clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS}/${trackSheet.client.id}`
        );
        if (res?.custom_fields) {
          const merged = res.custom_fields.map((field: any) => {
            const fieldValue = parsedCustomFields[field.id] || "";
            return {
              ...field,
              value: fieldValue,
            };
          });
          setCustomFieldsForClient(merged);
          if (merged && Array.isArray(merged)) {
            const customFieldValues: { [key: string]: string } = {};
            merged.forEach((field: any) => {
              const fieldName = `custom_${field.name}`;
              customFieldValues[fieldName] = field.value || "";
            });
            form.reset({ ...form.getValues(), ...customFieldValues });
          } else {
            setCustomFieldsForClient([]);
          }
        } else {
          setCustomFieldsForClient([]);
        }
      } catch (error) {
        console.error("Error fetching custom fields:", error);
      } finally {
        setCustomFieldsLoading(false);
      }
    }
  };

  async function onSubmit(values: any) {
    try {
      const customFieldsData: { [key: string]: string } = {};
      if (customFieldsForClient && Array.isArray(customFieldsForClient)) {
        customFieldsForClient.forEach((field: any) => {
          const fieldName = `custom_${field.name}`;
          if (values[fieldName] !== undefined) {
            customFieldsData[field.id] = values[fieldName];
          }
        });
      }

      const formData: any = {
        id: trackSheet.id,
        clientId: values.clientId,
        company: values.company,
        division: values.division,
        masterInvoice: values.masterInvoice,
        invoice: values.invoice,
        bol: values.bol,
        receivedDate: values.receivedDate,
        invoiceDate: values.invoiceDate,
        shipmentDate: values.shipmentDate,
        carrierId: values.carrierId,
        invoiceTotal: values.invoiceTotal,
        currency: values.currency,
        qtyShipped: values.qtyShipped,
        weightUnitName: values.weightUnitName,
        savings: values.savings,
        invoiceType: values.invoiceType,
        quantityBilledText: values.quantityBilledText,
        invoiceStatus: values.invoiceStatus,
        manualMatching: values.manualMatching,
        freightClass: values.freightClass,
        notes: values.notes,
        mistake: values.mistake,
        docAvailable: values.docAvailable ? values.docAvailable.join(",") : "",
        ftpFileName: values.ftpFileName,
        ftpPage: values.ftpPage,
        customFields: JSON.stringify(customFieldsData),
        filePath: generatedFilePath || trackSheet.filePath,
      };

      const res = await formSubmit(
        `${trackSheets_routes.UPDATE_TRACK_SHEETS}/${trackSheet.id}`,
        "PUT",
        formData
      );
      if (res.success) {
        toast.success(res.message);
        setOpen(false);
        router.refresh();
        form.reset();
        setCustomFieldsReloadTrigger((prev) => prev + 1);
      } else {
        toast.error(res.message || "Something went wrong");
      }
    } catch (error) {
      toast.error("An error occurred while updating the track sheet.");
      console.error(error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger title="Update">
        <TriggerButton type="edit" />
      </DialogTrigger>
      <DialogContent className="max-w-6xl dark:bg-gray-800 overflow-y-auto max-h-[100vh]">
        <DialogHeader>
          <DialogHeading
            title="Update TrackSheet"
            description=""
          />
        </DialogHeader>
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-5 gap-4">
              <div className="col-span-5 flex justify-end">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild tabIndex={-1}>
                      <div
                        className={`w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 ${
                          generatedFilePath
                            ? "bg-green-500 hover:bg-green-600"
                            : "bg-orange-500 hover:bg-orange-600"
                        }`}
                        tabIndex={-1}
                        role="button"
                        aria-label="File path status"
                      >
                        !
                      </div>
                    </TooltipTrigger>
                    <TooltipContent
                      side="top"
                      align="center"
                      className="z-[9999]"
                    >
                      <div className="text-sm max-w-md">
                        <p className="font-medium">File Path Status</p>
                        {generatedFilePath ? (
                          <div>
                            <p className="font-medium text-green-600 mb-2">
                              File Path Generated
                            </p>
                            <p className="text-xs font-mono break-all bg-gray-100 p-2 rounded text-black">
                              {generatedFilePath}
                            </p>
                          </div>
                        ) : (
                          <div>
                            <p className="font-medium text-orange-600 mb-1">
                              Please fill the form to generate file path
                            </p>
                          </div>
                        )}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <SelectComp
                form={form}
                label="Client"
                name="clientId"
                placeholder="Select Client"
                isRequired
                disabled
              >
                {clientDataUpdate.map((client: any) => (
                  <SelectItem value={client?.id.toString()} key={client.id}>
                    {client.client_name}
                  </SelectItem>
                ))}
              </SelectComp>

              <FormInput
                form={form}
                label="FTP File Name"
                name="ftpFileName"
                type="text"
                className="mt-2"
                isRequired
              />

              <PageInput
                className="mt-2"
                form={form}
                label="FTP Page"
                name="ftpPage"
                isRequired
              />
              <SearchSelect
                form={form}
                name="carrierId"
                label="Carrier"
                placeholder="Search Carrier"
                isRequired
                options={carrierByClient}
              />

              <FormInput
                form={form}
                label="Company"
                name="company"
                type="text"
                className="mt-2"
                isRequired
              />

              <FormInput
                form={form}
                label="Division"
                name="division"
                type="text"
                placeholder="Enter Division"
              />

              <FormInput
                form={form}
                label="Manual Matching"
                name="manualMatching"
                type="text"
                isRequired
              />

              <FormInput
                form={form}
                label="Master Invoice"
                name="masterInvoice"
                type="text"
              />

              <FormInput
                form={form}
                label="Invoice"
                name="invoice"
                type="text"
                isRequired
              />

              <FormInput
                form={form}
                label="BOL"
                name="bol"
                type="text"
              />

              <FormInput
                form={form}
                label="Received Date"
                name="receivedDate"
                type="date"
                isRequired
                className="mt-2"
              />

              <FormInput
                form={form}
                label="Invoice Date"
                name="invoiceDate"
                type="date"
                isRequired
                className="mt-2"
              />

              <FormInput
                form={form}
                label="Shipment Date"
                name="shipmentDate"
                type="date"
                className="mt-2"
              />

              <FormInput
                className="mt-2"
                form={form}
                label="Invoice Total"
                name="invoiceTotal"
                type="text"
                isRequired
              />

              <SearchSelect
                form={form}
                name="currency"
                label="Currency"
                placeholder="Search currency"
                isRequired
                options={[
                  { value: "USD", label: "USD" },
                  { value: "CAD", label: "CAD" },
                  { value: "EUR", label: "EUR" },
                ]}
              />

              <FormInput
                form={form}
                label="Savings"
                name="savings"
                type="text"
              />
              <FormInput form={form} label="Notes" name="notes" type="text" />
              <FormInput
                form={form}
                label="Freight Class"
                name="freightClass"
                type="text"
                isRequired
              />

              <FormInput
                form={form}
                label="Weight Unit"
                name="weightUnitName"
                type="text"
              />
              <FormInput
                form={form}
                label="Quantity Billed"
                name="quantityBilledText"
                type="text"
              />
              <FormInput
                form={form}
                label="Quantity Shipped"
                name="qtyShipped"
                type="text"
              />
              <FormInput
                form={form}
                label="Invoice Type"
                name="invoiceType"
                type="text"
                isRequired
              />

              <FormInput
                form={form}
                label="Invoice Status"
                name="invoiceStatus"
                type="text"
              />

              {/* <FormInput
                form={form}
                label="Mistake"
                name="mistake"
                type="text"
              /> */}

              <FormCheckboxGroup
                form={form}
                label="Documents Available"
                name="docAvailable"
                options={[
                  { label: "Invoice", value: "Invoice" },
                  { label: "BOL", value: "Bol" },
                  { label: "POD", value: "Pod" },
                  { label: "Packages List", value: "Packages List" },
                  { label: "Other Documents", value: "Other Documents" },
                ]}
                className="flex-row gap-2 text-xs"
              />

              {customFieldsLoading ? (
                <div className="col-span-5">Loading custom fields...</div>
              ) : (
                customFieldsForClient &&
                customFieldsForClient.length > 0 &&
                customFieldsForClient.map((field: any) => {
                  const fieldName = `custom_${field.name}`;
                  return (
                    <FormInput
                      key={field.id}
                      form={form}
                      label={field.label || field.name}
                      name={fieldName}
                      type={
                        field.type === "DATE"
                          ? "date"
                          : field.type === "NUMBER"
                          ? "number"
                          : "text"
                      }
                      isRequired={field.required}
                      disable={field.type === "AUTO"}
                      placeholder={`Enter ${field.label || field.name}`}
                    />
                  );
                })
              )}
            </div>
            <div className="mt-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-700">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium">Current File Path:</h3>
              </div>
              <p className="text-xs font-mono break-all bg-gray-100 dark:bg-gray-800 p-2 rounded text-black dark:text-white">
                {generatedFilePath || trackSheet?.filePath}
              </p>
            </div>
            <DialogFooter>
              <button
                type="submit"
                className="bg-blue-500 text-white px-4 py-2 mt-2 rounded hover:bg-blue-600"
              >
                Update
              </button>
            </DialogFooter>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
};

export default UpdateTrackSheet;
