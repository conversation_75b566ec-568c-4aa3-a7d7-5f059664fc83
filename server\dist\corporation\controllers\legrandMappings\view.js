"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewLegrandMappingById = exports.viewLegrandMapping = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewLegrandMapping = async (req, res) => {
    try {
        const data = await prisma.legrandMapping.findMany({
            orderBy: { id: "desc" },
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(404).json({ message: "Rule not found" });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewLegrandMapping = viewLegrandMapping;
const viewLegrandMappingById = async (req, res) => {
    try {
        const { id } = req.params;
        const data = await prisma.legrandMapping.findUnique({
            where: { id: id },
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(404).json({ message: "Rule not found" });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewLegrandMappingById = viewLegrandMappingById;
//# sourceMappingURL=view.js.map