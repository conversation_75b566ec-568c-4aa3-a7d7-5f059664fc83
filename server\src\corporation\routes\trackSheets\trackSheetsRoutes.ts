import { Router } from "express";
import { authenticate } from "../../../middleware/authentication";
import { createTrackSheets } from "../../controllers/trackSheets/create";
import {
  getRecievedDatesByInvoice,
  viewTrackSheets,
  ViewTrackSheetsById,
  getInvoiceStats,
} from "../../controllers/trackSheets/view";
import { updateTrackSheets } from "../../controllers/trackSheets/update";
import { deleteTrackSheets } from "../../controllers/trackSheets/delete";
import { createOrUpdateManifestDetails } from "../../controllers/trackSheets/createManifest";
import { viewManifest } from "../../controllers/trackSheets/viewManifest";

const router = Router();

router.get(
  "/clients/:id",
  //  authenticate,
  viewTrackSheets
);

router.get(
  "/manifest/:id",
  // authenticate,
  viewManifest
);

router.get(
  "/dates",
  // authenticate,
  getRecievedDatesByInvoice
);

router.get(
  "/stats",
  // authenticate,
  getInvoiceStats
);

router.get(
  "/:id",
  // authenticate,
  ViewTrackSheetsById
);

router.post(
  "/",
  // authenticate,
  createTrackSheets
);

router.put(
  "/:id",
  // authenticate,
  updateTrackSheets
);

router.delete("/:id", authenticate, deleteTrackSheets);

router.post("/manifest",
    // authenticate,
    createOrUpdateManifestDetails);

export default router;
