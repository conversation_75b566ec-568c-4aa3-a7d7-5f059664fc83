import { Router } from "express";
import {
  createUser,
  excelUsers,
  uploadProfileImage,
} from "../controllers/create";
import { userLogin } from "../../utils/login";
import { updateUser } from "../controllers/update";
import { deleteUser } from "../controllers/delete";
import {
  getCurrentUserProfile,
  viewSession,
  viewUser,
} from "../controllers/view";
import { getUserCSA } from "../controllers/viewCSA";
import { logout, sessionlogout } from "../controllers/logout";
import { authenticate } from "../../middleware/authentication";
import { checkPermissionMiddleware } from "../../middleware/checkPermission";
import { excelClient } from "../controllers/client/create";

import multer from "multer";

const router = Router();
const DIR = "./src/public";

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, DIR);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + "-" + file.originalname);
  },
});
const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 },
}).single("file");

router.post(
  "/create-user",
  authenticate,
  checkPermissionMiddleware("USER MANAGEMENT", "create-user"),
  createUser
);

router.post("/excel", upload, excelUsers); //user

router.post("/login", userLogin);

router.post("/logout", logout);

router.post("/sessionlogout", sessionlogout);

router.get("/get-all-session", viewSession);
router.get(
  "/",
  authenticate,
  checkPermissionMiddleware("USER MANAGEMENT", "view-user"),
  viewUser
);
router.get(
  "/current",
  authenticate,
  checkPermissionMiddleware("USER MANAGEMENT", "view-user"),
  getCurrentUserProfile
);

router.put(
  "/update-user/:id",
  authenticate,
  checkPermissionMiddleware("USER MANAGEMENT", "update-user"),
  updateUser
);

router.delete(
  "/delete-user/:id",
  authenticate,
  checkPermissionMiddleware("USER MANAGEMENT", "delete-user"),
  deleteUser
);

router.post("/upload-profile-image", authenticate, upload, uploadProfileImage);

router.get("/:id/csa",
    // authenticate,
    getUserCSA
)
export default router;
