import { handleError } from "../../../utils/helpers";

// Helper function to parse DD/MM/YYYY format dates
const parseDDMMYYYYDate = (dateString: string): Date | null => {
  if (!dateString || dateString.trim() === '') return null;

  // Handle DD/MM/YYYY format
  const parts = dateString.split('/');
  if (parts.length === 3) {
    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10);
    const year = parseInt(parts[2], 10);

    // Validate the parts
    if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900) {
      // Create date in UTC to avoid timezone issues
      return new Date(Date.UTC(year, month - 1, day));
    }
  }

  // Fallback to regular Date parsing if format doesn't match
  const fallbackDate = new Date(dateString);
  return isNaN(fallbackDate.getTime()) ? null : fallbackDate;
};

export const trackSheetService = async (trackSheetData: any) => {
  try {
    const createdTrackSheet = await prisma.trackSheets.create({
      data: trackSheetData,
    });
    return createdTrackSheet;
  } catch (error) {
    throw error;
  }
};

export const customFieldsService = async (trackSheetId, customFieldsData) => {
  try {
    const mappings = [];

    for (const customField of customFieldsData) {
      const mapping = await prisma.trackSheetCustomFieldMapping.create({
        data: {
          tracksheetId: trackSheetId,
          customFieldId: customField.id,
          value: customField.value,
        },
      });
      mappings.push(mapping);
    }
    return mappings;
  } catch (error) {
    throw error;
  }
};

export const createTrackSheets = async (req, res) => {
  try {
    const createdTrackSheets = [];

    for (const entry of req.body.entries) {
      const { customFields, ...entryFields } = entry;

      const preparedTrackSheetData = {
        ...entryFields,
        clientId: Number(req.body.clientId),
        carrierId: entryFields.carrierId ? Number(entryFields.carrierId) : null,
        qtyShipped: entryFields.qtyShipped
          ? Number(entryFields.qtyShipped)
          : null,
        invoiceTotal: entryFields.invoiceTotal
          ? Number(entryFields.invoiceTotal)
          : null,
        invoiceDate: parseDDMMYYYYDate(entryFields.invoiceDate),
        receivedDate: parseDDMMYYYYDate(entryFields.receivedDate),
        shipmentDate: parseDDMMYYYYDate(entryFields.shipmentDate),
        billToClient: entryFields.billToClient === "yes" ? true : entryFields.billToClient === "no" ? false : null,
        docAvailable: Array.isArray(entryFields.docAvailable)
          ? entryFields.docAvailable.join(",")
          : entryFields.docAvailable || null,
      };

      Object.keys(preparedTrackSheetData).forEach((key) => {
        if (preparedTrackSheetData[key] === undefined) {
          delete preparedTrackSheetData[key];
        }
      });

      const createdTrackSheet = await trackSheetService(preparedTrackSheetData);
      createdTrackSheets.push(createdTrackSheet);

      if (
        customFields &&
        Array.isArray(customFields) &&
        customFields.length > 0
      ) {
        await customFieldsService(createdTrackSheet.id, customFields);
      }
    }

    return res.status(201).json({
      success: true,
      message: `${createdTrackSheets.length} TrackSheet(s) created successfully`,
      data: createdTrackSheets,
    });
  } catch (error) {
    console.error("Error creating tracksheet:", error);
    return handleError(res, error);
  }
};