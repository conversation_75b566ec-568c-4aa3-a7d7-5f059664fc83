"use client";
import React, { useState } from "react";
import { useSearchParams } from "next/navigation";
import {
  FaUserPlus,
  FaUserCircle,
  FaTruck,
  FaBuilding,
  FaShieldAlt,
  FaFileSignature,
  FaBriefcase,
  FaPenFancy,
  FaGripHorizontal,
  FaStream,
} from "react-icons/fa";
import { BiSolidCategory } from "react-icons/bi";
import { RiUserFill } from "react-icons/ri";
import { TbReportSearch, TbTicket } from "react-icons/tb";
import { colorThemes } from "@/components/ui/colorTheme";
import { FaFileArrowDown, FaRegFolderOpen } from "react-icons/fa6";
import { BsFillFileEarmarkSpreadsheetFill } from "react-icons/bs";

// Valid route check (can be kept if needed)
const isValidPath = (path: string): boolean => {
  const allowedPaths = [
    "/pms/manage_employee",
    "/pms/manage_branch",
    "/pms/manage_associate",
    "/pms/manage_client",
    "/pms/manage_carrier",
    "/pms/manage-roles",
    "/pms/manage_category",
    "/pms/manage_work_type",
    "/pms/manage_work_report",
    "/pms/customize_report",
    "/pms/addupdate_custom_fields",
    "/pms/arrange_custom_fields",
  ];
  return allowedPaths.includes(path);
};

const Routes = [
  {
    label: "Manage User",
    path: "/pms/manage_employee",
    permission: { module: "USER MANAGEMENT" },
    icon: <FaUserPlus />,
  },
  {
    label: "Manage Branch",
    path: "/pms/manage_branch",
    permission: { module: "BRANCH MANAGEMENT" },
    icon: <FaBuilding />,
  },
  {
    label: "Manage Associate",
    path: "/pms/manage_associate",
    permission: { module: "ASSOCIATE MANAGEMENT" },
    icon: <RiUserFill />,
  },
  {
    label: "Manage Client",
    path: "/pms/manage_client",
    permission: { module: "CLIENT MANAGEMENT", action: "create-client" },
    icon: <FaUserCircle />,
  },
  {
    label: "Manage Carrier",
    path: "/pms/manage_carrier",
    permission: { module: "CARRIER MANAGEMENT" },
    icon: <FaTruck />,
  },
  {
    label: "Manage Roles",
    path: "/pms/manage-roles",
    permission: { module: "ROLE MANAGEMENT" },
    icon: <FaShieldAlt />,
  },
  {
    label: "Manage Category",
    path: "/pms/manage_category",
    permission: { module: "CATEGORY MANAGEMENT" },
    icon: <BiSolidCategory />,
  },
  {
    label: "Manage Work Type",
    path: "/pms/manage_work_type",
    permission: { module: "WORKTYPE MANAGEMENT", action: "update-work" },
    icon: <FaBriefcase />,
  },
  {
    label: "Manage Work Report",
    path: "/pms/manage_work_report",
    permission: { module: "WORK REPORT" },
    icon: <TbReportSearch />,
  },
  {
    label: "Manage Report",
    path: "/pms/customize_report",
    permission: { module: "CUSTOMIZE REPORT" },
    icon: <FaFileSignature />,
  },
  {
    label: "Add/Update Custom Fields",
    path: "/pms/addupdate_custom_fields",
    permission: { module: "CUSTOM FIELD MANAGEMENT" },
    icon: <FaPenFancy />,
  },
  {
    label: "Arrange Custom Fields",
    path: "/pms/arrange_custom_fields",
    permission: { module: "CUSTOM FIELD ARRANGEMENT" },
    icon: <FaGripHorizontal />,
  },
  {
    label: "Manage File Path",
    path: "/pms/manage_file_path",
    permission: { module: "FILE PATH MANAGEMENT" },
    icon: <FaFileArrowDown />,
  },
  {
    label: "TrackSheets",
    path: "/pms/track_sheets",
    permission: { module: "TRACKSHEET REPORT" },
    icon: <BsFillFileEarmarkSpreadsheetFill />,
  },
  {
    label: "Manage Imported Files",
    path: "/user/trackSheets/imported-files",
    permission: { module: "IMPORTED FILES MANAGEMENT" },
    icon: <FaRegFolderOpen />,
  },
  {
    label: "Manage Pipelines",
    path: "/pms/manage_pipelines",
    permission: { module: "PIPELINE MANAGEMENT" },
    icon: <FaStream />,
  },
  {
    label: "Manage Tickets",
    path: "/pms/manage_tickets",
    permission: { module: "TICKET MANAGEMENT" },
    icon: <TbTicket />,
  }
];

const ManagementPage: React.FC = () => {
  const [isClient, setIsClient] = useState(false);
  const [permissions, setPermissions] = useState<any[]>([]);

  // Set on first render (client only)
  React.useLayoutEffect(() => {
    setIsClient(true);

    const storedPermissions = sessionStorage.getItem("permissions");
    if (storedPermissions) {
      try {
        const parsedPermissions = JSON.parse(storedPermissions);
        const validPermissions = parsedPermissions.filter((perm: any) => perm !== null);
        setPermissions(validPermissions.length > 0 ? validPermissions : ["admin"]);
      } catch (error) {
        setPermissions(["admin"]);
      }
    } else {
      setPermissions(["admin"]);
    }
  }, []);

  if (!isClient) return null; // SSR-safe

  const hasAllowAllPermission = permissions.includes("allow_all");

  const filteredManagementRoutes = hasAllowAllPermission
    ? Routes
    : permissions.includes("admin")
    ? Routes
    : Routes.filter((route) =>
        permissions.some(
          (permission) =>
            typeof permission === "string" &&
            permission === route.permission.module
        )
      );

  return (
    <div className="px-6 md:px-12 lg:px-20 pt-6 pb-10">
      <div className="text-center mb-10">
        <h1 className="text-4xl font-semibold text-gray-900 tracking-tight">
          Management Services
        </h1>
        <div className="w-24 h-1 bg-blue-400 mt-2 mx-auto rounded-full"></div>
        <p className="text-sm text-gray-500 mt-2">
          Centralized access to all administrative modules.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-x-6 gap-y-12 justify-items-center">
        {filteredManagementRoutes.length > 0 ? (
          filteredManagementRoutes.map((route) => {
            const theme = colorThemes[route.path] || "bg-gray-100 text-gray-800";
            return (
              <a
                key={route.path}
                href={route.path}
                className={`flex flex-col items-center justify-center w-40 h-32 ${theme} rounded-2xl shadow-[0_10px_15px_rgba(0,0,0,0.2)] hover:shadow-[0_20px_40px_rgba(0,0,0,0.4)] transition-all duration-300 transform hover:scale-105`}
              >
                <div className="text-3xl mb-1">{route.icon}</div>
                <p className="text-sm font-medium text-center">{route.label}</p>
              </a>
            );
          })
        ) : (
          <p className="text-gray-600 col-span-full text-center">
            No management services available for you.
          </p>
        )}
      </div>
    </div>
  );
};

export default ManagementPage;
