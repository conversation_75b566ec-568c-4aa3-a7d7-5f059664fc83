"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLegrandMapping = void 0;
const operation_1 = require("../../../utils/operation");
const createLegrandMapping = async (req, res) => {
    const { corporationID } = req;
    const fields = {
        businessUnit: req.body.businessUnit,
        legalName: req.body.legalName,
        customeCode: req.body.customeCode,
        shippingBillingName: req.body.shippingBillingName,
        shippingBillingAddress: req.body.shippingBillingAddress,
        location: req.body.location,
        zipPostal: req.body.zipPostal,
        aliasCity: req.body.aliasCity,
        aliasShippingNames: req.body.aliasShippingNames,
        corporationId: Number(corporationID),
    };
    await (0, operation_1.createItem)({
        model: "LegrandMapping",
        fieldName: "id",
        fields: fields,
        res: res,
        req: req,
        successMessage: "LegrandMapping has been created",
    });
};
exports.createLegrandMapping = createLegrandMapping;
//# sourceMappingURL=create.js.map