# Pull Request (PR) Guidelines

This document serves as a guideline for both human reviewers and AI agents to maintain consistent PR quality standards.

# PR Scoring Metrics 📊

The PR score is calculated out of 100 points based on the following criteria:

## Core Requirements (50 points)
- PR Title Format (5 points)
  - Correct format `[TASK-ID] : Description`
  - Task ID in uppercase
- Branch Naming (5 points)
  - Follows pattern `<type>/TASK-ID-brief-description`
  - Valid type used
- Single Commit (10 points)
  - One commit only
  - Properly squashed
- Description Quality (20 points)
  - Minimum 15 words
  - Clear explanation of changes
  - Task link included
  - Impact analysis provided
- Code Organization (10 points)
  - Under 1000 lines
  - Focused changes
  - No unrelated changes

## Additional Quality Metrics (50 points)
- Code Size Optimization (10 points)
  - Under 400 lines (full points)
  - 400-700 lines (7 points)
  - 700-1000 lines (3 points)
- Code Quality (20 points)
  - Clear naming conventions (5 points)
  - No debug/commented code (5 points)
  - Code style compliance (5 points)
  - Logical organization (5 points)
- Documentation (15 points)
  - Relevant docs attached (5 points)
  - API/config changes documented (5 points)
  - Screenshots for UI changes (5 points)
- Technical Design (5 points)
  - Follows best practices
  - Efficient implementation

Score penalties:
- Missing MUST-HAVE requirement: -10 points each
- Poor code organization: -5 points
- Inadequate description: -5 points

# PR Tags 🏷️

The agent must add the following tags based on the review:
1. Status Tags:
   - `needs-changes` - When MUST-HAVEs are missing
   - `ready-to-merge` - When all MUST-HAVEs are met
2. Size Tags:
   - `size/small` - Under 200 lines
   - `size/medium` - 200-500 lines
   - `size/large` - 500-1000 lines
3. Type Tags:
   - Based on branch type: `type/feature`, `type/bugfix`, `type/hotfix`, `type/release`
4. Quality Tags:
   - `quality/excellent` - Score > 90
   - `quality/good` - Score 70-90
   - `quality/needs-improvement` - Score < 70

# MUST-HAVE Requirements ⚠️

If any of these requirements are not met, the PR must be marked for changes.

## 1. PR Naming and Structure
- PR title format must be: `[TASK-ID] : Description`
  - Example: `[XY-123] : Implement user authentication flow with Google OAuth2`
  - Task ID must be in uppercase (e.g., XY-123, AB-345)
- Branch naming convention must follow pattern: `<type>/TASK-ID-brief-description`
  - Valid types: feature, release, bugfix, hotfix
  - Example: `feature/XY-123-user-auth`
  - Example: `bugfix/AB-345-fix-login-error`

## 2. Single Commit Requirement
PRs must contain exactly ONE commit. To achieve this:
```bash
git fetch
git reset --soft $(git merge-base development HEAD)
git commit -am "[TASK-ID] : Your commit message"
git push --force
```

## 3. Code Organization
- Total changes must be less than 1000 lines
- Changes must be focused on a single task/feature
- No unrelated code changes

## 4. Description Requirements
- Minimum 15 words describing the changes
- Must include:
  - Detailed explanation of what is being changed
  - Clear justification of why the change is needed
  - Impact analysis (what areas are affected)
  - Direct link to the task/ticket (e.g., Jira, Azure DevOps)
  - Example format:
    ```
    Task Link: https://jira.company.com/browse/XY-123

    Changes:
    Implemented user authentication flow using Google OAuth2 integration. This includes
    the login button, callback handling, and user session management.

    Why:
    To provide users with a secure and convenient way to access our platform using
    their Google accounts, reducing friction in the onboarding process.

    Impact:
    - Auth module (/src/auth/*)
    - User profile components
    - Session handling middleware
    ```

# GOOD-TO-HAVE Recommendations 💡

These items are recommended but not mandatory. PRs can be approved with comments if these are missing.

## 1. Code Size and Organization
- Keep changes under 400 lines when possible
- Break large changes into smaller, logical PRs
- Avoid mixing refactoring with feature changes

## 2. Code Quality
- Follow existing code style and conventions
- Use clear, self-explanatory variable and function names
- Remove debug logs and commented-out code
- Add comments for complex logic

## 3. Documentation and References
- Attach relevant documentation links
  - Architecture diagrams
  - API specifications
  - Design documents
  - Requirements documents
- Include API changes or new configuration details
- Document any new environment variables
- Add screenshots for UI changes

# Review Process Instructions

## For AI Review Agents
As a reviewer, you must provide feedback in two places:
1. Post a concise review directly as a comment on the Pull Request
2. Return a detailed analysis in the conversation with the user

1. Initial Validation (MUST-HAVEs):
   - Check PR title format: `[TASK-ID] : Description`
   - Verify branch naming: `<type>/TASK-ID-brief-description`
   - Confirm single commit
   - Verify line count < 1000
   - Validate description meets requirements:
     - 15+ words
     - Contains task link
     - Explains what, why, and impact

2. Secondary Review (GOOD-TO-HAVEs):
   - Check if changes are under 400 lines
   - Look for attached documentation
   - Verify code style compliance
   - Check for debug/commented code

3. Response Format (Post this as a PR comment):
   ```markdown
   ### ✅ PR Review Summary
   Brief overview of the changes

   ### 📌 Must-Have Issues
   List any violations of mandatory requirements
   - [ ] Issue 1
   - [ ] Issue 2

   ### 🛠 Recommendations
   List any missing good-to-have items
   - Suggestion 1
   - Suggestion 2

   ### ✅ Review Decision
   - If all MUST-HAVEs met: APPROVED
   - If any MUST-HAVEs missing: CHANGES REQUESTED
     - List specific items that need fixing
   ```

4. Review Actions:
   - If any MUST-HAVEs are missing:
     - Post the concise review comment with the issues on the PR
     - Request changes on the PR
     - Add specific code comments on the relevant files for detailed feedback
     - Provide detailed analysis in the conversation, including:
       - Comprehensive explanation of each issue
       - Suggested fixes with examples
       - Additional context that might help the author
   - If only GOOD-TO-HAVEs are missing:
     - Post the review comment with recommendations on the PR
     - Approve the PR
     - Add inline suggestions where applicable
     - Provide detailed recommendations in the conversation, including:
       - In-depth explanation of each recommendation
       - Code examples or best practices
       - References to relevant documentation

5. Score and Tags Management:
   - Calculate PR score based on the scoring metrics
   - Add appropriate tags based on the review
   - Post score in a separate comment using the format:
     ```
     PR SCORE: [X]
     ```
   Note: Score must be posted only once and will be processed by PR_POST_PROCESSOR

6. Conversation Response Format:
   ```markdown
   ### 📋 Detailed PR Analysis
   Comprehensive overview of the changes and their impact

   ### 🔍 Technical Deep Dive
   Detailed analysis of the implementation, including:
   - Architecture considerations
   - Code patterns used
   - Potential edge cases
   - Performance implications

   ### 📌 Issues Found (if any)
   Detailed explanation of each issue:
   1. Issue Title
      - What: Detailed description
      - Why it's important
      - How to fix it
      - Example solution

   ### 💡 Improvement Suggestions
   In-depth recommendations:
   1. Suggestion Title
      - Current approach
      - Recommended approach
      - Benefits of changing
      - Example implementation

   ### 📊 Score Breakdown
   Total Score: [X]/100
   - Core Requirements: [X]/50
     * PR Title & Branch: [X]/10
     * Single Commit: [X]/10
     * Description: [X]/20
     * Code Organization: [X]/10
   - Quality Metrics: [X]/50
     * Code Size: [X]/10
     * Code Quality: [X]/20
     * Documentation: [X]/15
     * Technical Design: [X]/5

   ### 🏷️ Applied Tags
   List of tags added to the PR:
   - [tag1]
   - [tag2]
   ...

   ### 📚 Additional Resources
   - Relevant documentation
   - Similar PRs or issues
   - Best practices references
   ```

## For Human Authors
1. Pre-submission checklist:
   - [ ] PR title follows format: `[TASK-ID] : Description`
   - [ ] Branch name follows pattern: `<type>/TASK-ID-brief-description`
   - [ ] Changes squashed to single commit
   - [ ] Description is 15+ words
   - [ ] Task link is included
   - [ ] No sensitive information in commits
   - [ ] Changes are under 1000 lines

2. Responding to reviews:
   - Must-Have issues require immediate fixes
   - Good-to-Have suggestions can be addressed or acknowledged
   - Ask for clarification if needed
