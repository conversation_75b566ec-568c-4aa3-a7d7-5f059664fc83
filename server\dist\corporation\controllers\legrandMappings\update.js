"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateLegrandMapping = void 0;
const operation_1 = require("../../../utils/operation");
const updateLegrandMapping = async (req, res) => {
    const id = req.params.id;
    const { corporationID } = req;
    const fields = {
        businessUnit: req.body.businessUnit,
        legalName: req.body.legalName,
        customeCode: req.body.customeCode,
        shippingBillingName: req.body.shippingBillingName,
        shippingBillingAddress: req.body.shippingBillingAddress,
        location: req.body.location,
        zipPostal: req.body.zipPostal,
        aliasCity: req.body.aliasCity,
        aliasShippingNames: req.body.aliasShippingNames,
    };
    await (0, operation_1.updateItem)({
        model: "LegrandMapping",
        fieldName: "id",
        fields: fields,
        id: String(id),
        res,
        req,
        successMessage: "LegrandMapping updated successfully",
    });
};
exports.updateLegrandMapping = updateLegrandMapping;
//# sourceMappingURL=update.js.map