import { createContext } from "react";

interface TrackSheetType {
  filterdata: any;
  deleteData: boolean;
  customFieldsReloadTrigger: number;
  setFilterData: React.Dispatch<React.SetStateAction<any>>;
  setDeletedData: React.Dispatch<React.SetStateAction<any>>;
  setCustomFieldsReloadTrigger: React.Dispatch<React.SetStateAction<any>>;
}

export const TrackSheetContext = createContext<TrackSheetType>({
  filterdata: [],
  deleteData: false,
  customFieldsReloadTrigger: 0,
  setFilterData: () => {},
  setDeletedData: () => {},
  setCustomFieldsReloadTrigger: ()=> {}
});
