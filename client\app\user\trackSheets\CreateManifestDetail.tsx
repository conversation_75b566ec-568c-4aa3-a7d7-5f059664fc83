import DialogHeading from "@/app/_component/DialogHeading";
import FormInput from "@/app/_component/FormInput";
import SubmitBtn from "@/app/_component/SubmitBtn";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { useContext, useState, useImperativeHandle, forwardRef } from "react";
import { z } from "zod";
import useDynamicForm from "@/lib/useDynamicForm";
import { formSubmit, getAllData } from "@/lib/helpers";
import { trackSheets_routes } from "@/lib/routePath";
import SelectComp from "@/app/_component/SelectComp";
import { SelectItem } from "@/components/ui/select";
import { toast } from "sonner";
import { TrackSheetContext } from "./TrackSheetContext";
import { ForwardedRef } from "react";
import { IoMdClose } from "react-icons/io";

// Manifest details schema
const createManifestSchema = z.object({
  trackSheetId: z.string().min(1, "Track Sheet ID is required"),
  manifestStatus: z.string().optional(),
  manifestDate: z.string().optional(),
  manifestNotes: z.string().optional(),
  actionRequired: z.string().optional(),
});

interface CreateManifestDetailProps {
  trackSheetId: string | number;
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  userData: any;
}

export interface CreateManifestDetailRef {
  fetchManifestDetails: (id?: string | number) => void;
}

export const CreateManifestDetail = forwardRef<
  CreateManifestDetailRef,
  CreateManifestDetailProps
>(
  (
    { trackSheetId, isDialogOpen, setIsDialogOpen, userData },
    ref: ForwardedRef<CreateManifestDetailRef>
  ) => {
    const { form } = useDynamicForm(createManifestSchema, {
      trackSheetId: trackSheetId ? String(trackSheetId) : "",
      manifestStatus: "",
      manifestDate: new Date().toISOString().slice(0, 10),
      manifestNotes: "",
      actionRequired: "NONE",
    });
    const [loading, setLoading] = useState(false);
    const [isEdit, setIsEdit] = useState(false);

    const { setCustomFieldsReloadTrigger } = useContext(TrackSheetContext);

    // Add fetchManifestDetails function
    const fetchManifestDetails = async (id = trackSheetId) => {
      if (!id) return;
      setLoading(true);
      try {
        const url = `${trackSheets_routes.GET_MANIFEST_DETAILS_BY_ID}/${id}`;
        const data = await getAllData(url);

        if (data && data.success && data.data) {
          form.reset({
            trackSheetId: String(id),
            manifestStatus: data.data.manifestStatus || "",
            manifestDate: data.data.manifestDate
              ? data.data.manifestDate.slice(0, 10)
              : "",
            manifestNotes: data.data.manifestNotes || "",
            actionRequired: data.data.actionRequired || "",
          });
          setIsEdit(true);
        } else {
          setIsEdit(false);
          form.reset({
            trackSheetId: id ? String(id) : "",
            manifestStatus: "",
            manifestDate: new Date().toISOString().slice(0, 10),
            manifestNotes: "",
            actionRequired: "NONE",
          });
        }
      } catch (e) {
        setIsEdit(false);
      } finally {
        setLoading(false);
      }
    };

    useImperativeHandle(ref, () => ({
      fetchManifestDetails,
    }));

    // Fetch manifest details when dialog opens
    const handleDialogOpenChange = (open) => {
      setIsDialogOpen(open);
    };

    async function onSubmit(values: any) {
      try {
        const formData: any = {
          trackSheetId: Number(values.trackSheetId),
          manifestStatus: values.manifestStatus,
          manifestDate: values.manifestDate,
          manifestNotes: values.manifestNotes,
          actionRequired: values.actionRequired,
        };
        if (isEdit) {
          formData.updatedBy = userData?.username || "";
        } else {
          formData.createdBy = userData?.username || "";
        }

        const data = await formSubmit(
          trackSheets_routes.CREATE_MANIFEST_DETAILS,
          "POST",
          formData
        );

        if (data.success || data.ok) {
          toast.success(
            data.message || `Manifest details ${isEdit ? "updated" : "saved"}`
          );
          setIsDialogOpen(false);
          form.reset();
          setCustomFieldsReloadTrigger((prev) => prev + 1);
        } else {
          toast.error(
            data.error ||
              `Failed to ${isEdit ? "update" : "save"} manifest details`
          );
        }
      } catch (error) {
        toast.error("An error occurred while saving manifest details.");
        console.error(error);
      } finally {
        setLoading(false);
      }
    }

    return (
      <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
        <DialogTrigger asChild>
          <span />
        </DialogTrigger>
        <DialogContent className="md:min-w-[50rem] min-w-[30rem]">
          <DialogHeading
            title={
              isEdit
                ? "UPDATE ADDITIONAL INFORMATION"
                : "ADD ADDITIONAL INFORMATION"
            }
            description={
              isEdit
                ? "Update Additional Information"
                : "Please Enter Additional Information"
            }
          />
          <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70">
            <IoMdClose className="text-2xl" />
          </DialogClose>
          {loading ? (
            <div className="text-center py-8">Loading...</div>
          ) : (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(
                  (values) => onSubmit(values),
                  (errors) => console.error("Validation Errors:", errors)
                )}
              >
                <div className="grid grid-cols-3 gap-5 items-center">
                  <SelectComp
                    form={form}
                    label="ORCA STATUS"
                    name="manifestStatus"
                    placeholder="Select Status"
                  >
                    <SelectItem value="HOLD">HOLD</SelectItem>
                    <SelectItem value="ENTRY">ENTRY</SelectItem>
                    <SelectItem value="REJECT">REJECT</SelectItem>
                  </SelectComp>
                  <FormInput
                    form={form}
                    label="REVIEW DATE"
                    name="manifestDate"
                    type="date"
                  />
                  <SelectComp
                    form={form}
                    label="ACTION REQUIRED FROM"
                    name="actionRequired"
                    placeholder="Select"
                  >
                    <SelectItem value="CLIENT">CLIENT</SelectItem>
                    <SelectItem value="CARRIER">CARRIER</SelectItem>
                    <SelectItem value="NONE">NONE</SelectItem>
                  </SelectComp>
                </div>
                <div className="mt-4">
                  <FormInput
                    form={form}
                    label="ORCA COMMENTS"
                    name="manifestNotes"
                    placeholder="Enter Notes"
                    type="text"
                  />
                </div>
                <SubmitBtn
                  className="w-full bg-primary text-secondary hover:bg-primary/90 mt-4"
                  text={isEdit ? "Update" : "Submit"}
                />
              </form>
            </Form>
          )}
        </DialogContent>
      </Dialog>
    );
  }
);

export default CreateManifestDetail;
