"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/manage_client/page",{

/***/ "(app-pages-browser)/./lib/routePath.ts":
/*!**************************!*\
  !*** ./lib/routePath.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   associate_routes: function() { return /* binding */ associate_routes; },\n/* harmony export */   branch_routes: function() { return /* binding */ branch_routes; },\n/* harmony export */   carrier_routes: function() { return /* binding */ carrier_routes; },\n/* harmony export */   category_routes: function() { return /* binding */ category_routes; },\n/* harmony export */   clientCustomFields_routes: function() { return /* binding */ clientCustomFields_routes; },\n/* harmony export */   client_routes: function() { return /* binding */ client_routes; },\n/* harmony export */   comment_routes: function() { return /* binding */ comment_routes; },\n/* harmony export */   corporation_routes: function() { return /* binding */ corporation_routes; },\n/* harmony export */   create_ticket_routes: function() { return /* binding */ create_ticket_routes; },\n/* harmony export */   customFields_routes: function() { return /* binding */ customFields_routes; },\n/* harmony export */   customFilepath_routes: function() { return /* binding */ customFilepath_routes; },\n/* harmony export */   customizeReport: function() { return /* binding */ customizeReport; },\n/* harmony export */   daily_planning: function() { return /* binding */ daily_planning; },\n/* harmony export */   daily_planning_details: function() { return /* binding */ daily_planning_details; },\n/* harmony export */   daily_planning_details_routes: function() { return /* binding */ daily_planning_details_routes; },\n/* harmony export */   employee_routes: function() { return /* binding */ employee_routes; },\n/* harmony export */   importedFiles_routes: function() { return /* binding */ importedFiles_routes; },\n/* harmony export */   legrandMapping_routes: function() { return /* binding */ legrandMapping_routes; },\n/* harmony export */   location_api: function() { return /* binding */ location_api; },\n/* harmony export */   location_api_prefix: function() { return /* binding */ location_api_prefix; },\n/* harmony export */   manualMatchingMapping_routes: function() { return /* binding */ manualMatchingMapping_routes; },\n/* harmony export */   pipeline_routes: function() { return /* binding */ pipeline_routes; },\n/* harmony export */   rolespermission_routes: function() { return /* binding */ rolespermission_routes; },\n/* harmony export */   search_routes: function() { return /* binding */ search_routes; },\n/* harmony export */   setup_routes: function() { return /* binding */ setup_routes; },\n/* harmony export */   superadmin_routes: function() { return /* binding */ superadmin_routes; },\n/* harmony export */   tag_routes: function() { return /* binding */ tag_routes; },\n/* harmony export */   ticket_routes: function() { return /* binding */ ticket_routes; },\n/* harmony export */   trackSheets_routes: function() { return /* binding */ trackSheets_routes; },\n/* harmony export */   upload_file: function() { return /* binding */ upload_file; },\n/* harmony export */   usertitle_routes: function() { return /* binding */ usertitle_routes; },\n/* harmony export */   workreport_routes: function() { return /* binding */ workreport_routes; },\n/* harmony export */   worktype_routes: function() { return /* binding */ worktype_routes; }\n/* harmony export */ });\nconst location_api_prefix = \"https://api.techlogixit.com\";\nconst BASE_URL = \"http://localhost:5001\";\nconst corporation_routes = {\n    CREATE_CORPORATION: \"\".concat(BASE_URL, \"/api/corporation/create-corporation\"),\n    LOGIN_CORPORATION: \"\".concat(BASE_URL, \"/api/corporation/login\"),\n    GETALL_CORPORATION: \"\".concat(BASE_URL, \"/api/corporation/get-all-corporation\"),\n    UPDATE_CORPORATION: \"\".concat(BASE_URL, \"/api/corporation/update-corporation\"),\n    DELETE_CORPORATION: \"\".concat(BASE_URL, \"/api/corporation/delete-corporation\"),\n    LOGOUT_CORPORATION: \"\".concat(BASE_URL, \"/api/corporation/logout\")\n};\nconst superadmin_routes = {\n    LOGIN_SUPERADMIN: \"\".concat(BASE_URL, \"/api/superAdmin/login\"),\n    CREATE_SUPERADMIN: \"\".concat(BASE_URL, \"/api/superAdmin/create-superadmin\"),\n    GETALL_SUPERADMIN: \"\".concat(BASE_URL, \"/api/superAdmin/get-all-superadmin\"),\n    UPDATE_SUPERADMIN: \"\".concat(BASE_URL, \"/api/superAdmin/update-superadmin\"),\n    DELETE_SUPERADMIN: \"\".concat(BASE_URL, \"/api/superAdmin/delete-superadmin\"),\n    LOGOUT_SUPERADMIN: \"\".concat(BASE_URL, \"/api/superAdmin/logout\")\n};\nconst carrier_routes = {\n    CREATE_CARRIER: \"\".concat(BASE_URL, \"/api/carrier/create-carrier\"),\n    GETALL_CARRIER: \"\".concat(BASE_URL, \"/api/carrier/get-all-carrier\"),\n    UPDATE_CARRIER: \"\".concat(BASE_URL, \"/api/carrier/update-carrier\"),\n    DELETE_CARRIER: \"\".concat(BASE_URL, \"/api/carrier/delete-carrier\"),\n    GET_CARRIER_BY_CLIENT: \"\".concat(BASE_URL, \"/api/carrier/get-carrier-by-client\"),\n    UPLOAD_CARRIER: \"\".concat(BASE_URL, \"/api/carrier/excelCarrier\"),\n    EXCEL_CARRIER: \"\".concat(BASE_URL, \"/api/carrier/export-carrier\"),\n    GET_CARRIER: \"\".concat(BASE_URL, \"/api/carrier/get-carrier\")\n};\nconst client_routes = {\n    CREATE_CLIENT: \"\".concat(BASE_URL, \"/api/clients/create-client\"),\n    GETALL_CLIENT: \"\".concat(BASE_URL, \"/api/clients/get-all-client\"),\n    UPDATE_CLIENT: \"\".concat(BASE_URL, \"/api/clients/update-client\"),\n    DELETE_CLIENT: \"\".concat(BASE_URL, \"/api/clients/delete-client\"),\n    UPLOAD_CLIENT: \"\".concat(BASE_URL, \"/api/clients/excelClient\"),\n    EXCEL_CLIENT: \"\".concat(BASE_URL, \"/api/clients/export-client\")\n};\nconst associate_routes = {\n    CREATE_ASSOCIATE: \"\".concat(BASE_URL, \"/api/associate/create-associate\"),\n    GETALL_ASSOCIATE: \"\".concat(BASE_URL, \"/api/associate/get-all-associate\"),\n    UPDATE_ASSOCIATE: \"\".concat(BASE_URL, \"/api/associate/update-associate\"),\n    DELETE_ASSOCIATE: \"\".concat(BASE_URL, \"/api/associate/delete-associate\")\n};\nconst worktype_routes = {\n    CREATE_WORKTYPE: \"\".concat(BASE_URL, \"/api/worktype/create-worktype\"),\n    GETALL_WORKTYPE: \"\".concat(BASE_URL, \"/api/worktype/get-all-worktype\"),\n    UPDATE_WORKTYPE: \"\".concat(BASE_URL, \"/api/worktype/update-worktype\"),\n    DELETE_WORKTYPE: \"\".concat(BASE_URL, \"/api/worktype/delete-worktype\")\n};\nconst category_routes = {\n    CREATE_CATEGORY: \"\".concat(BASE_URL, \"/api/category/create-category\"),\n    GETALL_CATEGORY: \"\".concat(BASE_URL, \"/api/category/get-all-category\"),\n    UPDATE_CATEGORY: \"\".concat(BASE_URL, \"/api/category/update-category\"),\n    DELETE_CATEGORY: \"\".concat(BASE_URL, \"/api/category/delete-category\")\n};\nconst branch_routes = {\n    CREATE_BRANCH: \"\".concat(BASE_URL, \"/api/branch/create-branch\"),\n    GETALL_BRANCH: \"\".concat(BASE_URL, \"/api/branch/get-all-branch\"),\n    UPDATE_BRANCH: \"\".concat(BASE_URL, \"/api/branch/update-branch\"),\n    DELETE_BRANCH: \"\".concat(BASE_URL, \"/api/branch/delete-branch\")\n};\nconst employee_routes = {\n    LOGIN_USERS: \"\".concat(BASE_URL, \"/api/users/login\"),\n    LOGOUT_USERS: \"\".concat(BASE_URL, \"/api/users/logout\"),\n    LOGOUT_SESSION_USERS: \"\".concat(BASE_URL, \"/api/users/sessionlogout\"),\n    CREATE_USER: \"\".concat(BASE_URL, \"/api/users/create-user\"),\n    GETALL_USERS: \"\".concat(BASE_URL, \"/api/users\"),\n    GETALL_SESSION: \"\".concat(BASE_URL, \"/api/users//get-all-session\"),\n    GETCURRENT_USER: \"\".concat(BASE_URL, \"/api/users/current\"),\n    UPDATE_USERS: \"\".concat(BASE_URL, \"/api/users/update-user\"),\n    DELETE_USERS: \"\".concat(BASE_URL, \"/api/users/delete-user\"),\n    UPLOAD_USERS_IMAGE: \"\".concat(BASE_URL, \"/api/users/upload-profile-image\"),\n    UPLOAD_USERS_FILE: \"\".concat(BASE_URL, \"/api/users/excel\"),\n    GET_CSA: (id)=>\"\".concat(BASE_URL, \"/api/users/\").concat(id, \"/csa\")\n};\nconst usertitle_routes = {\n    CREATE_USERTITLE: \"\".concat(BASE_URL, \"/api/usertitle//get-all-usertitle\"),\n    GETALL_USERTITLE: \"\".concat(BASE_URL, \"/api/usertitle/get-all-usertitle\")\n};\nconst setup_routes = {\n    CREATE_SETUP: \"\".concat(BASE_URL, \"/api/client-carrier/create-setup\"),\n    GETALL_SETUP: \"\".concat(BASE_URL, \"/api/client-carrier/get-all-setup\"),\n    GETALL_SETUP_BYID: \"\".concat(BASE_URL, \"/api/client-carrier/get-all-setupbyId\"),\n    UPDATE_SETUP: \"\".concat(BASE_URL, \"/api/client-carrier/update-setup\"),\n    DELETE_SETUP: \"\".concat(BASE_URL, \"/api/client-carrier/delete-setup\"),\n    EXCEL_SETUP: \"\".concat(BASE_URL, \"/api/client-carrier/excelClientCarrier\")\n};\nconst location_api = {\n    GET_COUNTRY: \"\".concat(location_api_prefix, \"/api/location/country\"),\n    GET_STATE: \"\".concat(location_api_prefix, \"/api/location/statename\"),\n    GET_CITY: \"\".concat(location_api_prefix, \"/api/location/citybystate\")\n};\nconst workreport_routes = {\n    CREATE_WORKREPORT: \"\".concat(BASE_URL, \"/api/workreport/create-workreport\"),\n    CREATE_WORKREPORT_MANUALLY: \"\".concat(BASE_URL, \"/api/workreport/create-workreport-manually\"),\n    GETALL_WORKREPORT: \"\".concat(BASE_URL, \"/api/workreport/get-all-workreport\"),\n    GET_USER_WORKREPORT: \"\".concat(BASE_URL, \"/api/workreport/get-user-workreport\"),\n    GET_CURRENT_USER_WORKREPORT: \"\".concat(BASE_URL, \"/api/workreport/get-current-user-workreport\"),\n    UPDATE_WORKREPORT: \"\".concat(BASE_URL, \"/api/workreport/update-workreport\"),\n    DELETE_WORKREPORT: \"\".concat(BASE_URL, \"/api/workreport/delete-workreport\"),\n    UPDATE_WORK_REPORT: \"\".concat(BASE_URL, \"/api/workreport/update-workreports\"),\n    EXCEL_REPORT: \"\".concat(BASE_URL, \"/api/workreport/get-workreport\"),\n    GET_CURRENT_USER_WORKREPORT_STATUS_COUNT: \"\".concat(BASE_URL, \"/api/workreport\")\n};\nconst customizeReport = {\n    EXPORT_CUSTOMIZE_REPORT: \"\".concat(BASE_URL, \"/api/customizeReport/reports\")\n};\nconst rolespermission_routes = {\n    GETALL_ROLES: \"\".concat(BASE_URL, \"/api/rolespermission/get-all-roles\"),\n    ADD_ROLE: \"\".concat(BASE_URL, \"/api/rolespermission/add-roles\"),\n    GETALL_PERMISSION: \"\".concat(BASE_URL, \"/api/rolespermission/get-all-permissions\"),\n    UPDATE_ROLE: \"\".concat(BASE_URL, \"/api/rolespermission/update-roles\"),\n    DELETE_ROLE: \"\".concat(BASE_URL, \"/api/rolespermission/delete-roles\")\n};\nconst upload_file = {\n    UPLOAD_FILE: \"\".concat(BASE_URL, \"/api/upload/upload-file\"),\n    UPLOAD_FILE_TWOTEN: \"\".concat(BASE_URL, \"/api/upload/upload-csv-twoten\")\n};\nconst daily_planning_details = {\n    CREATE_DAILY_PLANNING_DETAILS: \"\".concat(BASE_URL, \"/api/dailyplanning/create-dailyplanningdetails\")\n};\nconst daily_planning = {\n    CREATE_DAILY_PLANNING: \"\".concat(BASE_URL, \"/api/dailyplanning/create-dailyplanning\"),\n    GETALL_DAILY_PLANNING: \"\".concat(BASE_URL, \"/api/dailyplanning/get-all-dailyplanning\"),\n    GETSPECIFIC_DAILY_PLANNING: \"\".concat(BASE_URL, \"/api/dailyplanning/get-specific-dailyplanning\"),\n    GET_DAILY_PLANNING_BY_ID: \"\".concat(BASE_URL, \"/api/dailyplanning/get-dailyplanning-by-id\"),\n    UPDATE_DAILY_PLANNING: \"\".concat(BASE_URL, \"/api/dailyplanning/update-dailyplanning\"),\n    DELETE_DAILY_PLANNING: \"\".concat(BASE_URL, \"/api/dailyplanning/delete-dailyplanning\"),\n    GET_USER_DAILY_PLANNING_BY_VISIBILITY: \"\".concat(BASE_URL, \"/api/dailyplanning/get-user-dailyplanningByVisibility\")\n};\nconst daily_planning_details_routes = {\n    CREATE_DAILY_PLANNING_DETAILS: \"\".concat(BASE_URL, \"/api/dailyplanningdetails/create-dailyplanningdetails\"),\n    EXCEL_DAILY_PLANNING_DETAILS: \"\".concat(BASE_URL, \"/api/dailyplanningdetails/excel-dailyplanningdetails\"),\n    GETALL_DAILY_PLANNING_DETAILS: \"\".concat(BASE_URL, \"/api/dailyplanningdetails/get-specific-dailyplanningdetails\"),\n    GET_DAILY_PLANNING_DETAILS_ID: \"\".concat(BASE_URL, \"/api/dailyplanningdetails/get-all-dailyplanningdetails\"),\n    GET_DAILY_PLANNING_DETAILS_MANUAL: \"\".concat(BASE_URL, \"/api/dailyplanningdetails\"),\n    GET_DAILY_PLANNING_DETAILS_TYPE: \"\".concat(BASE_URL, \"/api/dailyplanningdetails\"),\n    UPDATE_DAILY_PLANNING_DETAILS: \"\".concat(BASE_URL, \"/api/dailyplanningdetails/update-dailyplanningdetails\"),\n    DELETE_DAILY_PLANNING_DETAILS: \"\".concat(BASE_URL, \"/api/dailyplanningdetails/delete-dailyplanningdetails\"),\n    UPDATE_DAILY_PLANNING_DETAILS_STATEMENT: \"\".concat(BASE_URL, \"/api/dailyplanningdetails/update-dailyplanningdetails-statement\")\n};\nconst search_routes = {\n    GET_SEARCH: \"\".concat(BASE_URL, \"/api/search\")\n};\nconst trackSheets_routes = {\n    CREATE_TRACK_SHEETS: \"\".concat(BASE_URL, \"/api/track-sheets\"),\n    GETALL_TRACK_SHEETS: \"\".concat(BASE_URL, \"/api/track-sheets/clients\"),\n    UPDATE_TRACK_SHEETS: \"\".concat(BASE_URL, \"/api/track-sheets\"),\n    DELETE_TRACK_SHEETS: \"\".concat(BASE_URL, \"/api/track-sheets\"),\n    GETALL_IMPORT_FILES: \"\".concat(BASE_URL, \"/api/track-sheets/imported-files\"),\n    GETALL_IMPORT_ERRORS: \"\".concat(BASE_URL, \"/api/track-sheets/import-errors\"),\n    GET_RECEIVED_DATES_BY_INVOICE: \"\".concat(BASE_URL, \"/api/track-sheets/dates\"),\n    GET_STATS: \"\".concat(BASE_URL, \"/api/track-sheets/stats\"),\n    CREATE_MANIFEST_DETAILS: \"\".concat(BASE_URL, \"/api/track-sheets/manifest\"),\n    GET_MANIFEST_DETAILS_BY_ID: \"\".concat(BASE_URL, \"/api/track-sheets/manifest\")\n};\nconst clientCustomFields_routes = {\n    GET_CLIENT_CUSTOM_FIELDS: \"\".concat(BASE_URL, \"/api/client-custom-fields/clients\")\n};\nconst legrandMapping_routes = {\n    GET_LEGRAND_MAPPINGS: \"\".concat(BASE_URL, \"/api/legrand-mappings\")\n};\nconst manualMatchingMapping_routes = {\n    GET_MANUAL_MATCHING_MAPPINGS: \"\".concat(BASE_URL, \"/api/manual-matching-mappings\")\n};\nconst customFields_routes = {\n    GET_ALL_CUSTOM_FIELDS: \"\".concat(BASE_URL, \"/api/custom-fields\"),\n    GET_CUSTOM_FIELDS_WITH_CLIENTS: \"\".concat(BASE_URL, \"/api/custom-fields-with-clients\"),\n    GET_MANDATORY_FIELDS: \"\".concat(BASE_URL, \"/api/mandatory-fields\")\n};\nconst importedFiles_routes = {\n    DELETE_IMPORTED_FILES: \"\".concat(BASE_URL, \"/api/track-sheet-import\"),\n    GETALL_IMPORTED_FILES: \"\".concat(BASE_URL, \"/api/track-sheet-import\"),\n    GETALL_IMPORT_ERRORS: \"\".concat(BASE_URL, \"/api/track-sheet-import/errors\"),\n    GET_TRACK_SHEETS_BY_IMPORT_ID: \"\".concat(BASE_URL, \"/api/track-sheet-import\"),\n    DOWNLOAD_TEMPLATE: \"\".concat(BASE_URL, \"/api/track-sheet-import/template\"),\n    UPLOAD_IMPORTED_FILE: \"\".concat(BASE_URL, \"/api/track-sheet-import/upload\"),\n    DOWNLOAD_IMPORTED_FILE: \"\".concat(BASE_URL, \"/api/track-sheet-import/download\")\n};\nconst customFilepath_routes = {\n    CREATE_CUSTOM_FILEPATH: \"\".concat(BASE_URL, \"/api/custom-filepath/cffpc/create\"),\n    GET_CUSTOM_FILEPATH: \"\".concat(BASE_URL, \"/api/custom-filepath\"),\n    GET_CLIENT_CUSTOM_FILEPATH: \"\".concat(BASE_URL, \"/api/custom-filepath/cffpc/view\"),\n    UPDATE_CUSTOM_FILEPATH: \"\".concat(BASE_URL, \"/api/custom-filepath/cffpc/update\"),\n    DELETE_CUSTOM_FILEPATH: \"\".concat(BASE_URL, \"/api/custom-filepath/cffpc/delete\")\n};\nconst pipeline_routes = {\n    GET_PIPELINE: \"\".concat(BASE_URL, \"/api/pipelines\"),\n    ADD_PIPELINE: \"\".concat(BASE_URL, \"/api/pipelines\"),\n    UPDATE_PIPELINE: (id)=>\"\".concat(BASE_URL, \"/api/pipelines/\").concat(id),\n    DELETE_PIPELINE: (id)=>\"\".concat(BASE_URL, \"/api/pipelines/\").concat(id),\n    ADD_PIPELINE_STAGE: (id)=>\"\".concat(BASE_URL, \"/api/pipelines/\").concat(id, \"/stages\"),\n    UPDATE_PIPELINE_STAGE: (id)=>\"\".concat(BASE_URL, \"/api/pipeline-stages/\").concat(id),\n    DELETE_PIPELINE_STAGE: (id)=>\"\".concat(BASE_URL, \"/api/pipeline-stages/\").concat(id),\n    REORDER_PIPELINE_STAGES: (id)=>\"\".concat(BASE_URL, \"/api/pipelines/\").concat(id, \"/orders\"),\n    GET_PIPELINE_WORKTYPE: \"\".concat(BASE_URL, \"/api/pipelines/workTypes\")\n};\nconst create_ticket_routes = {\n    CREATE_TICKET: \"\".concat(BASE_URL, \"/api/tickets\"),\n    GET_TICKETS: \"\".concat(BASE_URL, \"/api/tickets\"),\n    GET_TICKETS_BY_ID: (id)=>\"\".concat(BASE_URL, \"/api/tickets/\").concat(id),\n    UPDATE_TICKETS: (id)=>\"\".concat(BASE_URL, \"/api/tickets/\").concat(id),\n    DELETE_TICKETS: (id)=>\"\".concat(BASE_URL, \"/api/tickets/\").concat(id),\n    GET_CSA: (id)=>\"\".concat(BASE_URL, \"/api/users/\").concat(id, \"/csa\")\n};\nconst ticket_routes = {\n    GET_TICKETS: \"\".concat(BASE_URL, \"/api/tickets\"),\n    GET_TICKET_BY_ID: (id)=>\"\".concat(BASE_URL, \"/api/tickets/\").concat(id),\n    CREATE_TICKET: \"\".concat(BASE_URL, \"/api/tickets\"),\n    UPDATE_TICKET: (id)=>\"\".concat(BASE_URL, \"/api/tickets/ticket/\").concat(id),\n    DELETE_TICKET: (id)=>\"\".concat(BASE_URL, \"/api/tickets/\").concat(id),\n    BULK_UPDATE_TICKETS: \"\".concat(BASE_URL, \"/api/tickets/bulk\"),\n    GET_TICKET_STAGE_LOGS: (ticketId)=>\"\".concat(BASE_URL, \"/api/tickets/\").concat(ticketId, \"/stage-logs\"),\n    EXPORT_TICKETS: \"\".concat(BASE_URL, \"/api/tickets/export\"),\n    GET_CURRENT_USER_TICKETS: \"\".concat(BASE_URL, \"/api/tickets/mine\")\n};\nconst comment_routes = {\n    CREATE_COMMENT: \"\".concat(BASE_URL, \"/api/comments\"),\n    GET_ALL_COMMENTS: \"\".concat(BASE_URL, \"/api/comments\"),\n    GET_COMMENTS_BY_TICKET: (ticketId)=>\"\".concat(BASE_URL, \"/api/comments/ticket/\").concat(ticketId),\n    UPDATE_COMMENT: (id)=>\"\".concat(BASE_URL, \"/api/comments/\").concat(id),\n    DELETE_COMMENT: (id)=>\"\".concat(BASE_URL, \"/api/comments/\").concat(id)\n};\nconst tag_routes = {\n    CREATE_TAG: \"\".concat(BASE_URL, \"/api/tags\"),\n    GET_ALL_TAGS: \"\".concat(BASE_URL, \"/api/tags\"),\n    GET_TAGS_BY_TICKET: (ticketId)=>\"\".concat(BASE_URL, \"/api/tags/ticket/\").concat(ticketId),\n    UPDATE_TAG: (id)=>\"\".concat(BASE_URL, \"/api/tags/\").concat(id),\n    DELETE_TAG: (id)=>\"\".concat(BASE_URL, \"/api/tags/\").concat(id),\n    ASSIGN_TAGS_TO_TICKET: \"\".concat(BASE_URL, \"/api/tags/assign\")\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/routePath.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/zodSchema.ts":
/*!**************************!*\
  !*** ./lib/zodSchema.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddDailyPlanningDetailsSchema: function() { return /* binding */ AddDailyPlanningDetailsSchema; },\n/* harmony export */   AddDailyPlanningSchema: function() { return /* binding */ AddDailyPlanningSchema; },\n/* harmony export */   AddRolesFormSchema: function() { return /* binding */ AddRolesFormSchema; },\n/* harmony export */   LoginSchema: function() { return /* binding */ LoginSchema; },\n/* harmony export */   actualNumberSchema: function() { return /* binding */ actualNumberSchema; },\n/* harmony export */   addTaskSchema: function() { return /* binding */ addTaskSchema; },\n/* harmony export */   addTaskSchemaPlus: function() { return /* binding */ addTaskSchemaPlus; },\n/* harmony export */   addWorkReportSchema: function() { return /* binding */ addWorkReportSchema; },\n/* harmony export */   addWorkTypeSchema: function() { return /* binding */ addWorkTypeSchema; },\n/* harmony export */   corporationSchema: function() { return /* binding */ corporationSchema; },\n/* harmony export */   createAssociateSchema: function() { return /* binding */ createAssociateSchema; },\n/* harmony export */   createBranchSchema: function() { return /* binding */ createBranchSchema; },\n/* harmony export */   createCarrierSchema: function() { return /* binding */ createCarrierSchema; },\n/* harmony export */   createCategorySchema: function() { return /* binding */ createCategorySchema; },\n/* harmony export */   createClientCarrierSchema: function() { return /* binding */ createClientCarrierSchema; },\n/* harmony export */   createClientSchema: function() { return /* binding */ createClientSchema; },\n/* harmony export */   createEmployeeSchema: function() { return /* binding */ createEmployeeSchema; },\n/* harmony export */   createFilepathSchema: function() { return /* binding */ createFilepathSchema; },\n/* harmony export */   manualAddTaskSchema: function() { return /* binding */ manualAddTaskSchema; },\n/* harmony export */   selectClientSchema: function() { return /* binding */ selectClientSchema; },\n/* harmony export */   updateCorporationSchema: function() { return /* binding */ updateCorporationSchema; },\n/* harmony export */   updateEmployeeSchema: function() { return /* binding */ updateEmployeeSchema; },\n/* harmony export */   updateWorkReportSchema: function() { return /* binding */ updateWorkReportSchema; },\n/* harmony export */   workReportSchema: function() { return /* binding */ workReportSchema; }\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var luxon__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! luxon */ \"(app-pages-browser)/./node_modules/luxon/src/luxon.js\");\n\n\nconst LoginSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    username: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Username is required\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Password is required\").min(8, \"Password must have at least 8 characters\")\n});\nconst createEmployeeSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    username: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Username is required\"),\n    firstName: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"FirstName is required\"),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"LastName is required\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Email is required\").email(\"Invalid email\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Password is required\").min(8, \"Password must have at least 8 characters\"),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Password is required\").min(8, \"Password must have at least 8 characters.\"),\n    user_type: zod__WEBPACK_IMPORTED_MODULE_1__.z.enum([\n        \"HR\",\n        \"TL\",\n        \"CSA\",\n        \"MEMBER\"\n    ]).optional(),\n    country: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    state: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    city: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    address: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    role_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Role is required\"),\n    level: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Title is required\"),\n    parent_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    branch: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Branch is required\"),\n    date_of_joining: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().nonempty(\"Date of joining is required\").refine((val)=>{\n        const today = new Date();\n        const inputDate = new Date(val);\n        return inputDate <= today;\n    }, {\n        message: \"Date of joining cannot be in the future\"\n    }),\n    clients: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.number()).optional()\n}).superRefine((data, rtx)=>{\n    if (data.password !== data.confirmPassword) {\n        rtx.addIssue({\n            code: zod__WEBPACK_IMPORTED_MODULE_1__.z.ZodIssueCode.custom,\n            path: [\n                \"confirmPassword\"\n            ],\n            message: \"Passwords do not match\"\n        });\n    }\n    if (data.level !== \"5\" && (!data.parent_id || data.parent_id.trim() === \"\")) {\n        rtx.addIssue({\n            code: zod__WEBPACK_IMPORTED_MODULE_1__.z.ZodIssueCode.custom,\n            path: [\n                \"parent_id\"\n            ],\n            message: \"Reporting To is required \"\n        });\n    }\n    if (data.level !== \"5\" && (!data.clients || data.clients.length === 0)) {\n        rtx.addIssue({\n            code: zod__WEBPACK_IMPORTED_MODULE_1__.z.ZodIssueCode.custom,\n            path: [\n                \"clients\"\n            ],\n            message: \"At least one client must be selected\"\n        });\n    }\n});\nconst updateEmployeeSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    username: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Username is required\"),\n    firstName: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"firstName is required\"),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"lastName is required\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Email is required\").email(\"Invalid email\"),\n    // user_type: z.enum([\"HR\", \"TL\", \"CSA\", \"NORMAL_MEMBER\"]).optional(),\n    role_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Role is required\"),\n    level: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Title is required\"),\n    parent_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    branch: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Branch is required\"),\n    date_of_joining: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().nonempty(\"Date of joining is required\").refine((val)=>{\n        const today = new Date();\n        const inputDate = new Date(val);\n        return inputDate <= today;\n    }, {\n        message: \"Date of joining cannot be in the future\"\n    }),\n    clients: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.number()).optional()\n}).superRefine((data, ctx)=>{\n    if (data.level !== \"5\" && (!data.parent_id || data.parent_id.trim() === \"\")) {\n        ctx.addIssue({\n            code: zod__WEBPACK_IMPORTED_MODULE_1__.z.ZodIssueCode.custom,\n            path: [\n                \"parent_id\"\n            ],\n            message: \"Reporting To is required \"\n        });\n    }\n    if (data.level !== \"5\" && (!data.clients || data.clients.length === 0)) {\n        ctx.addIssue({\n            code: zod__WEBPACK_IMPORTED_MODULE_1__.z.ZodIssueCode.custom,\n            path: [\n                \"clients\"\n            ],\n            message: \"Please select at least one client\"\n        });\n    }\n});\nconst createClientSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    associate: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Associate is required\"),\n    client_name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Client name is required\"),\n    // ownership: z.string().min(1, \"Ownership is required\"),\n    ownership: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Ownership is required\"),\n    branch: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"branch is required\")\n});\nconst createCarrierSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Carrier Name is required\"),\n    carrier_2nd_name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Carrier Name - 2 is required\"),\n    carrier_code: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"VAP ID is required\")\n});\nconst addWorkTypeSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    work_type: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Work type is required\"),\n    category: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Category is required\"),\n    does_it_require_planning_number: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    is_work_carrier_specific: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    is_backlog_regular_required: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional()\n});\nconst addTaskSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    description: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    date: zod__WEBPACK_IMPORTED_MODULE_1__.z.any(),\n    startTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.any(),\n    endTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.date().optional(),\n    clientName: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Client name is required\").optional(),\n    carrierName: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    workType: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Work type is required\"),\n    category: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Category is required\"),\n    planningNumber: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    duration: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n    expectedCompletionTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.any(),\n    actualNumber: zod__WEBPACK_IMPORTED_MODULE_1__.z.any(),\n    actualCompletionTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.any(),\n    timerDuration: zod__WEBPACK_IMPORTED_MODULE_1__.z.any()\n});\nconst addTaskSchemaPlus = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    description: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    date: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Date is required\"),\n    startTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Start time is required\").regex(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i, \"Start time must be in hh:mm AM/PM format.\").transform((value)=>value.toUpperCase()),\n    endTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"End time is required\").regex(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i, \"End time must be in hh:mm AM/PM format.\").transform((value)=>value.toUpperCase()),\n    clientName: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Client is required\"),\n    carrierName: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Carrier is required\"),\n    workType: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Work type is required\"),\n    category: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional(),\n    planningNumber: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    duration: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    expectedCompletionTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.any(),\n    actualNumber: zod__WEBPACK_IMPORTED_MODULE_1__.z.any(),\n    actualCompletionTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.any(),\n    timerDuration: zod__WEBPACK_IMPORTED_MODULE_1__.z.any(),\n    task_type: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional()\n}).refine((data)=>{\n    const today = luxon__WEBPACK_IMPORTED_MODULE_0__.DateTime.now();\n    const selectedDate = luxon__WEBPACK_IMPORTED_MODULE_0__.DateTime.fromISO(data.date);\n    if (selectedDate.hasSame(today, \"day\")) {\n        const currentTime = luxon__WEBPACK_IMPORTED_MODULE_0__.DateTime.now();\n        const startTime = luxon__WEBPACK_IMPORTED_MODULE_0__.DateTime.fromFormat(data.startTime, \"hh:mm a\");\n        return startTime <= currentTime;\n    }\n    return true;\n}, {\n    message: \"Future start times are not allowed.\",\n    path: [\n        \"startTime\"\n    ]\n}).refine((data)=>{\n    const startTime = luxon__WEBPACK_IMPORTED_MODULE_0__.DateTime.fromFormat(data.startTime, \"hh:mm a\");\n    const endTime = luxon__WEBPACK_IMPORTED_MODULE_0__.DateTime.fromFormat(data.endTime, \"hh:mm a\");\n    return endTime > startTime; // Ensure endTime is strictly greater than startTime\n}, {\n    message: \"End time should be greater than the start time.\",\n    path: [\n        \"endTime\"\n    ]\n}).refine((data)=>{\n    const today = luxon__WEBPACK_IMPORTED_MODULE_0__.DateTime.now();\n    const selectedDate = luxon__WEBPACK_IMPORTED_MODULE_0__.DateTime.fromISO(data.date);\n    if (selectedDate.hasSame(today, \"day\")) {\n        const currentTime = luxon__WEBPACK_IMPORTED_MODULE_0__.DateTime.now();\n        const endTime = luxon__WEBPACK_IMPORTED_MODULE_0__.DateTime.fromFormat(data.endTime, \"hh:mm a\");\n        return endTime <= currentTime;\n    }\n    return true;\n}, {\n    message: \"Future end times are not allowed.\",\n    path: [\n        \"endTime\"\n    ]\n});\nconst manualAddTaskSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    notes: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Notes are required\"),\n    date: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Date is required\"),\n    startTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Start time is required\").regex(/^(0[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i, \"Start time must be in hh:mm AM/PM format.\").transform((value)=>value.toUpperCase()),\n    endTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"End time is required\").regex(/^(0[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i, \"End time must be in hh:mm AM/PM format.\").transform((value)=>value.toUpperCase())\n}).refine((data)=>{\n    const today = new Date();\n    const selectedDate = new Date(data.date);\n    if (selectedDate.toDateString() === today.toDateString()) {\n        const currentTime = new Date();\n        // Parse time strings to Date objects for comparison\n        const startTime = convertTimeTo24HourFormat(data.startTime);\n        return startTime <= currentTime;\n    }\n    return true;\n}, {\n    message: \"Future start times are not allowed.\",\n    path: [\n        \"startTime\"\n    ]\n}).refine((data)=>{\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Remove time part to compare only the date\n    const selectedDate = new Date(data.date);\n    selectedDate.setHours(0, 0, 0, 0);\n    return selectedDate <= today;\n}, {\n    message: \"Future dates are not allowed.\",\n    path: [\n        \"date\"\n    ]\n}).refine((data)=>{\n    const startTime = convertTimeTo24HourFormat(data.startTime);\n    const endTime = convertTimeTo24HourFormat(data.endTime);\n    return endTime > startTime;\n}, {\n    message: \"End time should be greater than the start time.\",\n    path: [\n        \"endTime\"\n    ]\n}).refine((data)=>{\n    const today = new Date();\n    const selectedDate = new Date(data.date);\n    if (selectedDate.toDateString() === today.toDateString()) {\n        const currentTime = new Date();\n        const endTime = convertTimeTo24HourFormat(data.endTime);\n        return endTime <= currentTime;\n    }\n    return true;\n}, {\n    message: \"Future end times are not allowed.\",\n    path: [\n        \"endTime\"\n    ]\n});\n// Helper function to convert 12-hour time format to 24-hour format\nfunction convertTimeTo24HourFormat(time) {\n    const date = new Date();\n    const [hourMin, modifier] = time.split(\" \"); // Split time from AM/PM part\n    let [hours, minutes] = hourMin.split(\":\").map(Number); // Split hour and minute\n    // Convert to 24-hour format\n    if (modifier === \"PM\" && hours < 12) hours += 12;\n    if (modifier === \"AM\" && hours === 12) hours = 0;\n    date.setHours(hours, minutes, 0, 0); // Set hours and minutes\n    return date;\n}\nconst corporationSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    corporation_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().int().optional(),\n    username: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Username is required\").max(255, \"Username is too long\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().email(\"Invalid email format\").max(255, \"Email is too long\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(8, \"Password must have at least 8 characters\"),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(8, \"Password must have at least 8 characters\"),\n    country: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255, \"Country is too long\"),\n    state: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255, \"State is too long\"),\n    city: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255, \"City is too long\"),\n    address: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255, \"Address is too long\")\n});\nconst selectClientSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    client_name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255, \"Name is too long\"),\n    client_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.string()\n});\nconst updateCorporationSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    corporation_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().int().optional(),\n    username: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Username is required\").max(255, \"Username is too long\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().email(\"Invalid email format\").max(255, \"Email is too long\"),\n    country: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255, \"Country is too long\"),\n    state: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255, \"State is too long\"),\n    city: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255, \"City is too long\"),\n    address: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255, \"Address is too long\")\n});\nconst workReportSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    work_report_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().int().optional(),\n    date: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().refine((value)=>!isNaN(new Date(value).getTime()), {\n        message: \"Invalid date format\"\n    }),\n    // .transform((value) => new Date(value)),\n    id: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().int(),\n    client_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().int(),\n    carrier_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().int(),\n    work_type_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().int(),\n    category: zod__WEBPACK_IMPORTED_MODULE_1__.z.enum([\n        \"AUDIT\",\n        \"ENTRY\",\n        \"REPORT\"\n    ]),\n    planning_nummbers: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255).optional(),\n    expected_time: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().refine((value)=>!isNaN(new Date(value).getTime()), {\n        message: \"Invalid date format for expected time\"\n    }),\n    // .transform((value) => new Date(value)),\n    actual_number: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255).optional(),\n    start_time: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().refine((value)=>!isNaN(new Date(value).getTime()), {\n        message: \"Invalid time format for start time\"\n    }),\n    // .transform((value) => new Date(value)),\n    finish_time: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().refine((value)=>!isNaN(new Date(value).getTime()), {\n        message: \"Invalid time format for finish time\"\n    }),\n    // .transform((value) => new Date(value)),\n    time_spent: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().refine((value)=>!isNaN(new Date(value).getTime()), {\n        message: \"Invalid time format for time spent\"\n    }),\n    // .transform((value) => new Date(value)),\n    notes: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().max(255)\n});\nconst AddRolesFormSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Name is required\"),\n    permission: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.number()).optional(),\n    action: zod__WEBPACK_IMPORTED_MODULE_1__.z.any().optional()\n});\nconst AddDailyPlanningSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    daily_planning_date: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Date is required\"),\n    client_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Client is required\")\n});\nconst AddDailyPlanningDetailsSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_1__.z.enum([\n        \"INVOICE_ENTRY_STATUS\",\n        \"STATEMENT_TABLE\"\n    ]),\n    carrier: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    old: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    new: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    total: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    ute: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    receive_date: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    no_invoices: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    amount_of_invoice: zod__WEBPACK_IMPORTED_MODULE_1__.z.union([\n        zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n        zod__WEBPACK_IMPORTED_MODULE_1__.z.number()\n    ]).optional(),\n    currency: zod__WEBPACK_IMPORTED_MODULE_1__.z.enum([\n        \"USD\",\n        \"CAD\",\n        \"KRW\"\n    ]).optional(),\n    shipping_type: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    reconcile_date: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    division: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    send_date: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional()\n}).superRefine((data, ctx)=>{\n    if (data.type === \"INVOICE_ENTRY_STATUS\") {\n        // Require fields for \"invoice entry status\"\n        [\n            \"carrier\",\n            \"old\",\n            \"new\",\n            \"total\",\n            \"ute\"\n        ].forEach((field)=>{\n            if (!data[field]) {\n                ctx.addIssue({\n                    code: zod__WEBPACK_IMPORTED_MODULE_1__.z.ZodIssueCode.custom,\n                    path: [\n                        field\n                    ],\n                    message: \"\".concat(field, ' is required when planningType is \"invoice entry status\"')\n                });\n            }\n        });\n    } else if (data.type === \"STATEMENT_TABLE\") {\n        // Require fields for \"statement table\"\n        [\n            \"receive_date\",\n            \"no_invoices\",\n            \"amount_of_invoice\",\n            \"currency\",\n            \"reconcile_date\",\n            \"shipping_type\",\n            // \"division\",\n            \"carrier\"\n        ].forEach((field)=>{\n            if (!data[field]) {\n                ctx.addIssue({\n                    code: zod__WEBPACK_IMPORTED_MODULE_1__.z.ZodIssueCode.custom,\n                    path: [\n                        field\n                    ],\n                    message: \"\".concat(field, ' is required when planningType is \"statement table\"')\n                });\n            }\n        });\n    }\n});\nconst createClientCarrierSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    carrier_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().nonempty(\"Carrier is required\"),\n    client_id: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().nonempty(\"Client is required\"),\n    payment_terms: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().nonempty(\"Payment terms are required\"),\n    client_name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional()\n});\nconst actualNumberSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    actual_number: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, {\n        message: \"Please enter a actual number\"\n    }),\n    notes: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional()\n});\nconst createCategorySchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Name is required\")\n});\nconst createBranchSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Name is required\")\n});\nconst createAssociateSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Name is required\")\n});\nconst createFilepathSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Client is required\"),\n    filepath: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Filepath is required\")\n});\nconst addWorkReportSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    date: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Date is required\"),\n    username: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Username is required\"),\n    clientname: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Client name is required\"),\n    carriername: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Carrier name is required\"),\n    work_type: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Work type is required\"),\n    category: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Category is required\"),\n    actual_number: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Actual number is required\"),\n    start_time: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Start time is required\"),\n    finish_time: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Finish time is required\"),\n    time_spent: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Time spent is required\"),\n    notes: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Notes are required\"),\n    task_type: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Task type is required\")\n});\nconst updateWorkReportSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    date: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    startTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional() // Allow the start time to be empty or undefined\n    .refine((value)=>value === undefined || /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i.test(value), \"Start time must be in hh:mm AM/PM format.\").transform((value)=>value ? value.toUpperCase() : value),\n    endTime: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional() // Allow the end time to be empty or undefined\n    .refine((value)=>value === undefined || /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i.test(value), \"End time must be in hh:mm AM/PM format.\").transform((value)=>value ? value.toUpperCase() : value),\n    module: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    action: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional()\n}).refine((data)=>{\n    const today = new Date();\n    const selectedDate = new Date(data.date);\n    // Only check future start times if the start time is provided\n    if (data.startTime) {\n        const currentTime = new Date();\n        const startTime = convertTimeTo24HourFormat(data.startTime);\n        if (selectedDate.toDateString() === today.toDateString()) {\n            return startTime <= currentTime;\n        }\n    }\n    return true; // Skip check if no start time\n}, {\n    message: \"Future start times are not allowed.\",\n    path: [\n        \"startTime\"\n    ]\n}).refine((data)=>{\n    // Only check end time if both startTime and endTime are provided\n    if (data.startTime && data.endTime) {\n        const startTime = convertTimeTo24HourFormat(data.startTime);\n        const endTime = convertTimeTo24HourFormat(data.endTime);\n        return endTime > startTime;\n    }\n    return true; // Skip check if either is missing\n}, {\n    message: \"End time should be greater than the start time.\",\n    path: [\n        \"endTime\"\n    ]\n}).refine((data)=>{\n    // Only check future end times if the end time is provided\n    if (data.endTime) {\n        const today = new Date();\n        const selectedDate = new Date(data.date);\n        const currentTime = new Date();\n        const endTime = convertTimeTo24HourFormat(data.endTime);\n        if (selectedDate.toDateString() === today.toDateString()) {\n            return endTime <= currentTime;\n        }\n    }\n    return true; // Skip check if no end time\n}, {\n    message: \"Future end times are not allowed.\",\n    path: [\n        \"endTime\"\n    ]\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/zodSchema.ts\n"));

/***/ })

});