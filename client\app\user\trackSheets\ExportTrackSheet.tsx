import { Button } from "@/components/ui/button";
import { useState } from "react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import ExcelJS from "exceljs";
import { useRouter, useSearchParams } from "next/navigation";
import { formatDate } from "@/lib/swrFetching";
import { getAllData } from "@/lib/helpers";
import { trackSheets_routes } from "@/lib/routePath";

function tryParseCustomFields(fields: any): any {
  if (typeof fields === "string") {
    try {
      return JSON.parse(fields) || {};
    } catch (e) {
      console.error("Error parsing custom fields string:", e);
      return {};
    }
  }
  return fields || {};
}

// Helper function to clean string values
function cleanStringValue(value: any): string {
  if (value === null || value === undefined) return "";
  const stringValue = String(value);
  // Remove leading single quotes and escape any remaining single quotes
  return stringValue.replace(/^'/, "").replace(/'/g, "''");
}

const ExportTrackSheet = ({
  filteredTrackSheetData,
  customFieldsMap,
  selectedClients,
  columnVisibility,
  showOrcaColumns,
}: any) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);

  const Export = async () => {
    setIsLoading(true);
    try {
      let allData: any[] = [];

      let clientId = null;
      if (filteredTrackSheetData?.length > 0) {
        clientId =
          filteredTrackSheetData[0]?.client?.id ||
          filteredTrackSheetData[0]?.clientId;
      } else if (selectedClients?.length > 0) {
        clientId = selectedClients[0]?.value;
      }

      if (!clientId) throw new Error("No client selected");

      const params = new URLSearchParams(searchParams);
      params.delete("pageSize");
      params.delete("page");

      const baseUrl = `${
        trackSheets_routes.GETALL_TRACK_SHEETS
      }/${clientId}?${params.toString()}`;
      const response = await getAllData(baseUrl);
      allData = response.data || [];

      allData = allData.map((row) => ({
        ...row,
        customFields: (row.TrackSheetCustomFieldMapping || []).reduce(
          (acc: any, mapping: any) => {
            acc[mapping.customFieldId] = mapping.value;
            return acc;
          },
          {}
        ),
      }));

      const staticHeaders = [
        { key: "client", label: "Client" },
        { key: "company", label: "Company" },
        { key: "division", label: "Division" },
        { key: "carrier", label: "Carrier" },
        { key: "ftpFileName", label: "FTP File Name" },
        { key: "ftpPage", label: "FTP Page" },
        { key: "filePath", label: "File Path" },
        { key: "masterInvoice", label: "Master Invoice" },
        { key: "invoice", label: "Invoice" },
        { key: "bol", label: "Bol" },
        { key: "receivedDate", label: "Received Date" },
        { key: "invoiceDate", label: "Invoice Date" },
        { key: "shipmentDate", label: "Shipment Date" },
        { key: "invoiceTotal", label: "Invoice Total" },
        { key: "currency", label: "Currency" },
        { key: "qtyShipped", label: "Qty Shipped" },
        { key: "quantityBilledText", label: "Quantity Billed" },
        { key: "invoiceStatus", label: "Invoice Status" },
        { key: "manualMatching", label: "Manual Matching" },
        { key: "freightClass", label: "Freight Class" },
        { key: "invoiceType", label: "Invoice Type" },
        { key: "weightUnitName", label: "Weight Unit" },
        { key: "savings", label: "Savings" },
        { key: "billToClient", label: "Bill To Client" },
        { key: "docAvailable", label: "Doc Available" },
        { key: "notes", label: "Notes" },
        { key: "enteredBy", label: "Entered By" },
      ];

      // Add ORCA-specific columns if showOrcaColumns is true
      if (showOrcaColumns) {
        staticHeaders.push(
          { key: "manifestStatus", label: "ORCA STATUS" },
          { key: "manifestDate", label: "REVIEW Date" },
          { key: "actionRequired", label: "ACTION REQUIRED FROM" },
          { key: "manifestNotes", label: "ORCA NOTES" }
        );
      }

      const visibleStaticHeaders = staticHeaders.filter(
        (header) => columnVisibility[header.key] !== false
      );

      const customFieldIds = Object.keys(customFieldsMap || {});
      const visibleCustomFieldHeaders = customFieldIds
        .filter((id) => columnVisibility[`customField_${id}`] !== false)
        .map((id) => ({
          key: `customField_${id}`,
          label: customFieldsMap[id]?.name || `Custom Field ${id}`,
        }));

      const allHeaders = [
        ...visibleStaticHeaders,
        ...visibleCustomFieldHeaders,
      ];

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("TrackSheet Report");

      worksheet.columns = allHeaders.map((header) => ({
        header: header.label,
        key: header.key,
        width: header.label === "File Path" ? 50 : 20,
      }));

      allData.forEach((item) => {
        const rowData: any = {};
        visibleStaticHeaders.forEach((header) => {
          const value = item[header.key];
          switch (header.key) {
            case "client":
              rowData[header.key] = cleanStringValue(item.client?.client_name);
              break;
            case "carrier":
              rowData[header.key] = cleanStringValue(item.carrier?.name);
              break;
            case "receivedDate":
            case "invoiceDate":
            case "shipmentDate":
              if (!value) {
                rowData[header.key] = "N/A";
              } else {
                const date = new Date(value);
                rowData[header.key] = !isNaN(date.getTime()) ? date : "N/A";
              }
              break;
            case "billToClient":
              rowData[header.key] =
                value === true ? "Yes" : value === false ? "No" : "N/A";
              break;
            case "manifestStatus":
              rowData[header.key] = cleanStringValue(item.manifestDetails?.manifestStatus);
              break;
            case "manifestDate":
              if (!item.manifestDetails?.manifestDate) {
                rowData[header.key] = "N/A";
              } else {
                const date = new Date(item.manifestDetails.manifestDate);
                rowData[header.key] = !isNaN(date.getTime()) ? date : "N/A";
              }
              break;
            case "manifestNotes":
              rowData[header.key] = cleanStringValue(item.manifestDetails?.manifestNotes);
              break;
            case "actionRequired":
              rowData[header.key] = cleanStringValue(item.manifestDetails?.actionRequired);
              break;
              default:
              rowData[header.key] = cleanStringValue(value);
          }
        });

        const itemCustomFields = tryParseCustomFields(item.customFields);
        visibleCustomFieldHeaders.forEach((header) => {
          const fieldId = header.key.replace("customField_", "");
          const rawValue = itemCustomFields[fieldId];
          const fieldType = customFieldsMap[fieldId]?.type;

          if (!rawValue) {
            rowData[header.key] = "";
          } else if (fieldType === "DATE") {
            const parsedDate = new Date(rawValue);
            rowData[header.key] = !isNaN(parsedDate.getTime())
              ? parsedDate
              : null;
          } else {
            rowData[header.key] = cleanStringValue(rawValue);
          }
        });

        worksheet.addRow(rowData);
      });

      // Apply date format to all date columns (yyyy-mm-dd)
      worksheet.columns.forEach((col) => {
        if (
          ["receivedDate", "invoiceDate", "shipmentDate", "manifestDate"].includes(
            col.key as string
          ) ||
          (col.key?.startsWith("customField_") &&
            customFieldsMap[col.key.replace("customField_", "")]?.type ===
              "DATE")
        ) {
          col.numFmt = "yyyy-mm-dd";
          // Re-apply alignment for date columns to ensure it's not overridden by numFmt
          col.alignment = { horizontal: "left", vertical: "middle" };
        }
      });

      const fileBuffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([fileBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `TrackSheet_Report_${
        new Date().toISOString().split("T")[0]
      }.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Export error:", error);
    } finally {
      setIsLoading(false);
      router.refresh();
    }
  };

  return (
    <div>
      <Button
        onClick={Export}
        className="mt-6 mb-1 px-3 py-1.5 max-h-8 hover:to-main-color-foreground mr-2 text-white font-semibold uppercase"
        disabled={isLoading}
      >
        {isLoading ? (
          <span className="animate-spin">
            <AiOutlineLoading3Quarters />
          </span>
        ) : (
          "Download report"
        )}
        {isLoading ? "Exporting..." : ""}
      </Button>
    </div>
  );
};

export default ExportTrackSheet;
