import { AllCommunityModule, ModuleRegistry, ColDef } from "ag-grid-community";
import { DateTime } from "luxon";
import PinnedHeader from "@/app/_component/PinnedHeader";
import UpdateTrackSheet from "./UpdateTrackSheet";
import { Copy, Check } from "lucide-react";
import { useState, useRef } from "react";
import { toast } from "sonner";
import DeleteRow from "@/app/_component/DeleteRow";
import { trackSheets_routes } from "@/lib/routePath";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import CreateManifestDetail, {
  CreateManifestDetailRef,
} from "./CreateManifestDetail";
import { MdCreateNewFolder, MdOutlineCreateNewFolder } from "react-icons/md";
import { Button } from "@/components/ui/button";

ModuleRegistry.registerModules([AllCommunityModule]);

// Copy Button Component for File Path
const CopyButton = ({
  text,
  disabled = false,
}: {
  text: string;
  disabled?: boolean;
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent row selection

    if (disabled || !text || text === "No file path generated") {
      toast.error("No file path to copy");
      return;
    }

    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      toast.success("File path copied to clipboard!");

      // Reset the copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy file path");
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <button
      onClick={handleCopy}
      disabled={disabled}
      className={`
        ml-2 p-1 rounded transition-all duration-200 flex-shrink-0
        ${
          disabled
            ? "text-gray-400 cursor-not-allowed"
            : "text-gray-600 hover:text-blue-600 hover:bg-blue-50 active:bg-blue-100"
        }
      `}
      title={disabled ? "No file path to copy" : "Copy file path"}
    >
      {copied ? (
        <Check className="w-4 h-4 text-green-600" />
      ) : (
        <Copy className="w-4 h-4" />
      )}
    </button>
  );
};

const Column = (
  permissions: any,
  setDeletedData: any,
  deleteData: any,
  carrierDataUpdate: any,
  clientDataUpdate: any,
  userData,
  { customFieldsMap, showOrcaColumns }: { customFieldsMap: any, showOrcaColumns?: boolean }
) => {
  
  const baseColumns: ColDef[] = [
    {
      field: "client",
      headerName: "Client",
      valueGetter: (params) => params.data?.client?.client_name || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "company",
      headerName: "Company",
      valueGetter: (params) => params.data?.company || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "division",
      headerName: "Division",
      valueGetter: (params) => params.data?.division || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "carrier",
      headerName: "Carrier",
      valueGetter: (params) => params.data?.carrier?.name || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "ftpFileName",
      headerName: "FTP File Name",
      valueGetter: (params) => params.data?.ftpFileName || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "ftpPage",
      headerName: "FTP Page",
      valueGetter: (params) => params.data?.ftpPage || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "filePath",
      headerName: "File Path",
      cellRenderer: (params: any) => {
        const filePath = params.data?.filePath;
        const hasFilePath = filePath && filePath !== "N/A";
        const displayText = hasFilePath ? filePath : "No file path generated";

        return (
          <div className="flex items-center w-full h-full gap-2 min-w-0">
            <CopyButton
              text={hasFilePath ? filePath : ""}
              disabled={!hasFilePath}
            />
            <span
              className={
                `truncate pr-2 min-w-0 flex-1 ` +
                (hasFilePath
                  ? "text-black font-mono text-xs"
                  : "text-gray-500 italic text-xs")
              }
              title={displayText}
              style={{ minWidth: 0 }}
            >
              {displayText}
            </span>
            <div className="flex-shrink-0"></div>
          </div>
        );
      },
      valueGetter: (params) => {
        const filePath = params.data?.filePath;
        if (!filePath || filePath === "N/A") {
          return "No file path generated";
        }
        return filePath;
      },
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 550,
      cellStyle: () => ({
        display: "flex",
        alignItems: "center",
        padding: "4px 8px",
        height: "100%",
        borderRight: "1px solid #e0e0e0",
        whiteSpace: "nowrap", // Prevent text wrapping
        overflow: "hidden", // Hide overflow
        textOverflow: "ellipsis", // Add ellipsis for overflowed text
      }),
      tooltipValueGetter: (params) => {
        const filePath = params.data?.filePath;
        if (!filePath || filePath === "N/A") {
          return "No file path generated - this entry was created before file path generation was implemented";
        }
        return filePath;
      },
      headerComponent: PinnedHeader,
    },
    {
      field: "MasterInvoice",
      headerName: "Master Invoice",
      valueGetter: (params) => params.data?.masterInvoice || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "invoice",
      headerName: "Invoice",
      valueGetter: (params) => params.data?.invoice || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "bol",
      headerName: "Bol",
      valueGetter: (params) => params.data?.bol || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "receivedDate",
      headerName: "Received Date",
      valueFormatter: (params) => {
        if (!params.value) return "N/A";
        const dt = DateTime.fromISO(params.value, { zone: "utc" });
        return dt.isValid ? dt.toFormat("dd-MM-yyyy") : "N/A";
      },
      filter: "agDateColumnFilter",
      filterParams: {
        comparator: (filterLocalDateAtMidnight: Date, cellValue: string) => {
          if (!cellValue) return -1;
          const cellDate = DateTime.fromISO(cellValue, { zone: "utc" })
            .startOf("day")
            .toJSDate();
          const filterDate = new Date(
            Date.UTC(
              filterLocalDateAtMidnight.getFullYear(),
              filterLocalDateAtMidnight.getMonth(),
              filterLocalDateAtMidnight.getDate()
            )
          );
          if (cellDate < filterDate) return -1;
          if (cellDate > filterDate) return 1;
          return 0;
        },
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "invoiceDate",
      headerName: "Invoice Date",
      valueGetter: (params) => {
        const date = params.data?.invoiceDate;
        return date
          ? DateTime.fromISO(date, { zone: "utc" }).toFormat("dd-MM-yyyy")
          : "N/A";
      },
      filter: "agDateColumnFilter",
      filterParams: {
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          if (!cellValue) return -1;
          const cellDate = DateTime.fromISO(cellValue, { zone: "utc" })
            .startOf("day")
            .toJSDate();
          const filterDate = new Date(
            Date.UTC(
              filterLocalDateAtMidnight.getFullYear(),
              filterLocalDateAtMidnight.getMonth(),
              filterLocalDateAtMidnight.getDate()
            )
          );
          if (cellDate < filterDate) return -1;
          if (cellDate > filterDate) return 1;
          return 0;
        },
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "shipmentDate",
      headerName: "Shipment Date",
      valueGetter: (params) => {
        const date = params.data?.shipmentDate;
        return date
          ? DateTime.fromISO(date, { zone: "utc" }).toFormat("dd-MM-yyyy")
          : "N/A";
      },
      filter: "agDateColumnFilter",
      filterParams: {
        comparator: (filterLocalDateAtMidnight, cellValue) => {
          if (!cellValue) return -1;
          const cellDate = DateTime.fromISO(cellValue, { zone: "utc" })
            .startOf("day")
            .toJSDate();
          const filterDate = new Date(
            Date.UTC(
              filterLocalDateAtMidnight.getFullYear(),
              filterLocalDateAtMidnight.getMonth(),
              filterLocalDateAtMidnight.getDate()
            )
          );
          if (cellDate < filterDate) return -1;
          if (cellDate > filterDate) return 1;
          return 0;
        },
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "invoiceTotal",
      headerName: "Invoice Total",
      valueGetter: (params) => params.data?.invoiceTotal || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "currency",
      headerName: "Currency",
      valueGetter: (params) => params.data?.currency || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "qtyShipped",
      headerName: "Qty Shipped",
      valueGetter: (params) => params.data?.qtyShipped || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },

    {
      field: "quantityBilledText",
      headerName: "Quantity Billed",
      valueGetter: (params) => params.data?.quantityBilledText || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "invoiceStatus",
      headerName: "Invoice Status",
      valueGetter: (params) => params.data?.invoiceStatus || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "manualMatching",
      headerName: "Manual Matching",
      valueGetter: (params) => params.data?.manualMatching || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "invoiceType",
      headerName: "Invoice Type",
      valueGetter: (params) => params.data?.invoiceType || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "weightUnitName",
      headerName: "Weight Unit",
      valueGetter: (params) => params.data?.weightUnitName || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "savings",
      headerName: "Savings",
      valueGetter: (params) => params.data?.savings || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "freightClass",
      headerName: "Freight Class",
      valueGetter: (params) => params.data?.freightClass || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "billToClient",
      headerName: "Bill To Client",
      valueGetter: (params) => {
        const billToClient = params.data?.billToClient;
        if (billToClient === true) return "Yes";
        if (billToClient === false) return "No";
        return "N/A";
      },
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "docAvailable",
      headerName: "Doc Available",
      valueGetter: (params) => params.data?.docAvailable || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "notes",
      headerName: "Notes",
      valueGetter: (params) => params.data?.notes || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    {
      field: "enteredBy",
      headerName: "Entered by",
      valueGetter: (params) => params.data?.enteredBy || "N/A",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
      width: 140,
      headerComponent: PinnedHeader,
    },
    // {
    //   field: "mistake",
    //   headerName: "Mistake",
    //   valueGetter: (params) => params.data?.mistake || "N/A",
    //   filter: "agTextColumnFilter",
    //   filterParams: {
    //     buttons: ["clear"],
    //   },
    //   width: 140,
    //   headerComponent: PinnedHeader,
    // },
  ];

  if (customFieldsMap && Object.keys(customFieldsMap).length > 0) {
    Object.keys(customFieldsMap).forEach((fieldId: string) => {
      const fieldMeta = customFieldsMap[fieldId];
      const fieldName = fieldMeta?.name || `Custom Field ${fieldId}`;
      const fieldType = fieldMeta?.type;
      baseColumns.push({
        field: `customField_${fieldId}`,
        headerName: fieldName,
        valueGetter: (params) => {
          const value = params.data?.customFields?.[fieldId];
          if (!value) return "N/A";
          if (fieldType === "DATE") {
            const dt = DateTime.fromISO(value, { zone: "utc" });
            return dt.isValid ? dt.toFormat("dd-MM-yyyy") : value;
          }
          return value;
        },
        filter: "agTextColumnFilter",
        filterParams: { buttons: ["clear"] },
        width: 140,
        headerComponent: PinnedHeader,
      });
    });
  }

  // Add manifest fields after custom fields
  if (showOrcaColumns) {
    baseColumns.push(
      {
        field: "manifestStatus",
        headerName: "ORCA STATUS",
        valueGetter: (params) =>
          params.data?.manifestDetails?.manifestStatus || "N/A",
        filter: "agTextColumnFilter",
        filterParams: { buttons: ["clear"] },
        width: 140,
        headerComponent: PinnedHeader,
      },
      {
        field: "manifestDate",
        headerName: "REVIEW DATE",
        valueGetter: (params) => {
          const date = params.data?.manifestDetails?.manifestDate;
          return date
            ? DateTime.fromISO(date, { zone: "utc" }).toFormat("dd-MM-yyyy")
            : "N/A";
        },
        filter: "agDateColumnFilter",
        filterParams: {
          comparator: (filterLocalDateAtMidnight, cellValue) => {
            if (!cellValue) return -1;
            const cellDate = DateTime.fromISO(cellValue, { zone: "utc" })
              .startOf("day")
              .toJSDate();
            const filterDate = new Date(
              Date.UTC(
                filterLocalDateAtMidnight.getFullYear(),
                filterLocalDateAtMidnight.getMonth(),
                filterLocalDateAtMidnight.getDate()
              )
            );
            if (cellDate < filterDate) return -1;
            if (cellDate > filterDate) return 1;
            return 0;
          },
          buttons: ["clear"],
        },
        width: 140,
        headerComponent: PinnedHeader,
      },
      {
        field: "actionRequired",
        headerName: "ACTION REQUIRED FROM",
        valueGetter: (params) => params.data?.manifestDetails?.actionRequired || "N/A",
        filter: "agTextColumnFilter",
        filterParams: { buttons: ["clear"] },
        width: 140,
        headerComponent: PinnedHeader,
      },
      {
        field: "manifestNotes",
        headerName: "ORCA COMMENTS",
        valueGetter: (params) => params.data?.manifestDetails?.manifestNotes || "N/A",
        filter: "agTextColumnFilter",
        filterParams: { buttons: ["clear"] },
        width: 180,
        headerComponent: PinnedHeader,
      }
    );
  }

  // Add action column after custom fields
  baseColumns.push({
    field: "action",
    headerName: "Action",
    cellRenderer: (params: any) => {
      const TrackSheet = params?.data;      
      const [isDialogOpen, setIsDialogOpen] = useState(false);
      const manifestDetailRef = useRef<CreateManifestDetailRef | null>(null);

      const handleOpenDialog = () => {
        setIsDialogOpen(true);
        setTimeout(() => {
          manifestDetailRef.current?.fetchManifestDetails(TrackSheet?.id);
        }, 0);
      };

      return (
        <div className="flex justify-center items-center gap-2">
          {/* Only show Create Manifest button if orca columns are visible */}
          {showOrcaColumns && (
            <>
              <Button
                variant="customButton"
                className="cursor-pointer capitalize h-4 w-4 text-gray-600"
                onClick={handleOpenDialog}
              >
                <MdCreateNewFolder />
              </Button>
              {isDialogOpen && (
                <CreateManifestDetail
                  ref={manifestDetailRef}
                  trackSheetId={TrackSheet?.id}
                  isDialogOpen={isDialogOpen}
                  setIsDialogOpen={setIsDialogOpen}
                  userData={userData}
                />
              )}
            </>
          )}
          <UpdateTrackSheet
            trackSheet={TrackSheet}
            clientDataUpdate={clientDataUpdate}
            carrierDataUpdate={carrierDataUpdate}
          />
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["delete-trackSheet"]}
         >
            <DeleteRow
              route={`${trackSheets_routes.DELETE_TRACK_SHEETS}/${TrackSheet?.id}`}
              onSuccess={() => setDeletedData(!deleteData)}
            />
          </PermissionWrapper>
        </div>
      );
    },
    sortable: false,
    width: 120,
    pinned: "right",
    cellStyle: () => ({
      fontFamily: "inherit",
      textOverflow: "clip",
      color: "inherit",
      fontStyle: "normal",
    }),
  });

  return baseColumns;
};

export default Column;
