import { Router } from "express";
import { createTicket } from "../../controllers/ticket/create";
import { authenticate } from "../../../middleware/authentication";
import {
  viewTicket,
  viewTicketById,
  getTicketStageChangeLogs,
  getCurrentUserTickets,
} from "../../controllers/ticket/view";
import { deleteTicket } from "../../controllers/ticket/delete";
import {
  bulkUpdateTickets,
  ticketUpdate,
} from "../../controllers/ticket/update";
import { exportTicketService } from "../../controllers/ticket/exportTicketService";

const router = Router();
router.get(
    "/mine",
    authenticate,
    getCurrentUserTickets
  );
// More specific routes first
router.put("/ticket/:id",
    authenticate,
    ticketUpdate
)

router.get("/:id/stage-logs",
    authenticate,
    getTicketStageChangeLogs
)

router.put("/bulk",
    authenticate,
    bulkUpdateTickets
)

// Generic routes last
router.post("/",
    authenticate,
    createTicket);

router.get("/",
    authenticate,
    viewTicket );

router.get(
  "/export",
  // authenticate,
  exportTicketService
);

router.get("/:id",
    authenticate,
    viewTicketById
)
router.delete("/:id",
    authenticate,
    deleteTicket
)


export default router;
